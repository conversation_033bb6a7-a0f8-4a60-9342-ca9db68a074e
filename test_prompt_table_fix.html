<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试生成图片提示词表格修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .rich-editor-hidden {
            display: none;
        }
        .llm-functions {
            margin-top: 16px;
        }
        button {
            margin: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            background: #2563eb;
            color: #fff;
        }
        .prompt-table {
            width: 100%;
            margin-top: 16px;
            border-collapse: collapse;
        }
        .prompt-table th, .prompt-table td {
            border: 1px solid #ddd;
            padding: 6px;
        }
        .prompt-table th {
            background: #f3f4f6;
        }
        .test-result {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
        }
        .success {
            background: #f0fdf4;
            border-color: #22c55e;
        }
        .content-display {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>测试生成图片提示词表格修复</h1>
    
    <div class="test-section">
        <h2>修复说明</h2>
        <p>修复了"生成图片提示词表格"按钮无法正确获取LLM返回HTML消息表格内容的问题：</p>
        <ul>
            <li>✅ 改进了正则表达式匹配逻辑，支持多种格式的生图提示词</li>
            <li>✅ 添加了调试输出，方便排查问题</li>
            <li>✅ 增强了错误处理和用户提示</li>
            <li>✅ 支持基本HTML格式的内容解析</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试场景1：带【】括号的格式</h2>
        <div class="content-display">
            <h3>模拟LLM返回内容：</h3>
            <p><strong>第1页：</strong></p>
            <p><strong>主持人角色对白：</strong>在一个阳光明媚的早晨，小兔子贝贝在花园里发现了一道神奇的彩虹门。</p>
            <p><strong>生图Prompt详细描述：</strong>【图片风格为「3D渲染」，色彩鲜艳，小兔子贝贝站在花园中，面前有一道七彩彩虹门，阳光透过云层洒下，花朵盛开，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
        </div>
        
        <div class="rich-editor-hidden">
            <p><strong>第1页：</strong></p>
            <p><strong>主持人角色对白：</strong>在一个阳光明媚的早晨，小兔子贝贝在花园里发现了一道神奇的彩虹门。</p>
            <p><strong>生图Prompt详细描述：</strong>【图片风格为「3D渲染」，色彩鲜艳，小兔子贝贝站在花园中，面前有一道七彩彩虹门，阳光透过云层洒下，花朵盛开，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
        </div>
        
        <div class="llm-functions">
            <button onclick="testGenerateTable(0)">生成图片提示词表格</button>
        </div>
        <div id="result1" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>测试场景2：无括号的格式</h2>
        <div class="content-display">
            <h3>模拟LLM返回内容：</h3>
            <p><strong>第1页：</strong></p>
            <p><strong>主持人角色对白：</strong>小猫咪米米住在一个美丽的小镇上，她最喜欢探索新的地方。</p>
            <p><strong>生图Prompt详细描述：</strong>图片风格为3D渲染，色彩鲜艳，小猫咪米米在小镇街道上，周围有彩色房屋和绿树，阳光明媚，全景，特写，超精细，大师构图，背景虚幻，比例16:9</p>
        </div>
        
        <div class="rich-editor-hidden">
            <p><strong>第1页：</strong></p>
            <p><strong>主持人角色对白：</strong>小猫咪米米住在一个美丽的小镇上，她最喜欢探索新的地方。</p>
            <p><strong>生图Prompt详细描述：</strong>图片风格为3D渲染，色彩鲜艳，小猫咪米米在小镇街道上，周围有彩色房屋和绿树，阳光明媚，全景，特写，超精细，大师构图，背景虚幻，比例16:9</p>
        </div>
        
        <div class="llm-functions">
            <button onclick="testGenerateTable(1)">生成图片提示词表格</button>
        </div>
        <div id="result2" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>测试场景3：场景配图格式</h2>
        <div class="content-display">
            <h3>模拟LLM返回内容：</h3>
            <p><strong>第1页：</strong></p>
            <p><strong>主持人角色对白：</strong>小明是一个年轻的魔法师，他正在学习各种神奇的魔法。</p>
            <p><strong>场景配图，生图Prompt详细描述：</strong>【年轻魔法师小明手持魔法棒，身穿蓝色法袍，周围有闪闪发光的魔法粒子，魔法塔背景，中景，特写，超精细，大师构图，背景虚幻，比例16:9】</p>
        </div>
        
        <div class="rich-editor-hidden">
            <p><strong>第1页：</strong></p>
            <p><strong>主持人角色对白：</strong>小明是一个年轻的魔法师，他正在学习各种神奇的魔法。</p>
            <p><strong>场景配图，生图Prompt详细描述：</strong>【年轻魔法师小明手持魔法棒，身穿蓝色法袍，周围有闪闪发光的魔法粒子，魔法塔背景，中景，特写，超精细，大师构图，背景虚幻，比例16:9】</p>
        </div>
        
        <div class="llm-functions">
            <button onclick="testGenerateTable(2)">生成图片提示词表格</button>
        </div>
        <div id="result3" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>测试场景4：多页内容</h2>
        <div class="content-display">
            <h3>模拟LLM返回内容：</h3>
            <p><strong>第1页：</strong></p>
            <p><strong>生图Prompt详细描述：</strong>【第一页的提示词内容】</p>
            <p><strong>第2页：</strong></p>
            <p><strong>生图Prompt详细描述：</strong>第二页的提示词内容，无括号格式</p>
            <p><strong>第3页：</strong></p>
            <p><strong>场景配图，生图Prompt详细描述：</strong>【第三页的提示词内容】</p>
        </div>
        
        <div class="rich-editor-hidden">
            <p><strong>第1页：</strong></p>
            <p><strong>生图Prompt详细描述：</strong>【第一页的提示词内容】</p>
            <p><strong>第2页：</strong></p>
            <p><strong>生图Prompt详细描述：</strong>第二页的提示词内容，无括号格式</p>
            <p><strong>第3页：</strong></p>
            <p><strong>场景配图，生图Prompt详细描述：</strong>【第三页的提示词内容】</p>
        </div>
        
        <div class="llm-functions">
            <button onclick="testGenerateTable(3)">生成图片提示词表格</button>
        </div>
        <div id="result4" class="test-result"></div>
    </div>

    <script>
        function testGenerateTable(scenarioIndex) {
            console.log(`开始测试场景 ${scenarioIndex + 1}`);
            
            // 获取对应场景的富文本编辑区
            const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
            const richEditor = allRichEditors[scenarioIndex];
            const html = richEditor ? richEditor.innerHTML : '';

            // 添加调试输出
            console.log('获取到的HTML内容:', html);
            console.log('富文本编辑区数量:', allRichEditors.length);

            if (!html) {
                alert('未找到可解析的内容，请先生成故事内容！');
                return;
            }

            // 解析HTML，生成表格（使用修复后的逻辑）
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            const prompts = [];
            let pageNum = 1;
            let lastPage = '';
            
            tempDiv.querySelectorAll('p').forEach(p => {
                const txt = p.textContent || '';
                console.log('处理段落:', txt); // 调试输出
                
                // 提取页数
                let pageMatch = txt.match(/第([一二三四五六七八九十]+)页/);
                if (pageMatch) lastPage = pageMatch[0];
                
                // 改进的生图提示词匹配逻辑，支持多种格式
                let promptText = '';
                
                // 格式1：生图Prompt详细描述：【内容】
                let promptMatch1 = txt.match(/生图Prompt详细描述[：:]\s*[【]([^】]+)[】]/);
                if (promptMatch1) {
                    promptText = promptMatch1[1].trim();
                } else {
                    // 格式2：生图Prompt详细描述：内容（无括号）
                    let promptMatch2 = txt.match(/生图Prompt详细描述[：:]\s*(.+?)(?:\s*$|\s*。|\s*，)/);
                    if (promptMatch2) {
                        promptText = promptMatch2[1].trim();
                    } else {
                        // 格式3：场景配图，生图Prompt详细描述：内容
                        let promptMatch3 = txt.match(/场景配图[，,]\s*生图Prompt详细描述[：:]\s*[【]?([^】\n]+)[】]?/);
                        if (promptMatch3) {
                            promptText = promptMatch3[1].trim();
                        }
                    }
                }
                
                if (promptText) {
                    console.log('找到提示词:', promptText); // 调试输出
                    prompts.push({
                        index: prompts.length + 1,
                        page: lastPage || `第${pageNum}页`,
                        prompt: promptText
                    });
                    pageNum++;
                }
            });
            
            console.log('解析到的提示词数量:', prompts.length); // 调试输出
            
            // 显示结果
            const resultDiv = document.getElementById(`result${scenarioIndex + 1}`);
            
            if (prompts.length === 0) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h3>❌ 测试失败</h3>
                    <p>未找到生图提示词！请检查内容格式。</p>
                    <p>调试信息已输出到浏览器控制台，请按F12查看。</p>
                `;
                return;
            }
            
            // 生成表格HTML
            let tableHtml = `<table class='prompt-table'>\n`
                + `<thead>\n<tr>\n`
                + `<th>序号</th>`
                + `<th>页数</th>`
                + `<th>生图提示词</th>`
                + `<th>操作</th>`
                + `</tr>\n</thead>\n<tbody>`;
            prompts.forEach(row => {
                tableHtml += `<tr>`
                    + `<td style='text-align:center;'>${row.index}</td>`
                    + `<td style='text-align:center;'>${row.page}</td>`
                    + `<td>${row.prompt}</td>`
                    + `<td style='text-align:center;'>`
                    + `<a href='#' onclick='alert("查看提示词: ${row.prompt}")'>查看</a>`
                    + `</td></tr>`;
            });
            tableHtml += '</tbody></table>';
            
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                <h3>✅ 测试成功！</h3>
                <p><strong>解析到的提示词数量：</strong>${prompts.length}个</p>
                <p><strong>提示词内容：</strong></p>
                <ul>
                    ${prompts.map(p => `<li>${p.page}: ${p.prompt.substring(0, 50)}...</li>`).join('')}
                </ul>
                ${tableHtml}
            `;
            
            console.log('✅ 图片提示词表格生成成功！解析到', prompts.length, '个提示词');
        }
        
        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('生成图片提示词表格修复测试页面加载完成');
            console.log('修复要点：');
            console.log('1. 支持多种格式的生图提示词匹配');
            console.log('2. 添加了详细的调试输出');
            console.log('3. 改进了错误处理和用户提示');
            console.log('4. 兼容基本HTML格式的内容');
        };
    </script>
</body>
</html>
