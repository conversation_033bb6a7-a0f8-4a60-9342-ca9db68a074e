<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试纯文本格式修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .rich-editor-hidden {
            display: none;
        }
        .llm-functions {
            margin-top: 16px;
        }
        button {
            margin: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            background: #2563eb;
            color: #fff;
        }
        .prompt-table {
            width: 100%;
            margin-top: 16px;
            border-collapse: collapse;
        }
        .prompt-table th, .prompt-table td {
            border: 1px solid #ddd;
            padding: 6px;
        }
        .prompt-table th {
            background: #f3f4f6;
        }
        .test-result {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
        }
        .success {
            background: #f0fdf4;
            border-color: #22c55e;
        }
        .content-display {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-line;
        }
        .comparison {
            display: flex;
            gap: 20px;
        }
        .before, .after {
            flex: 1;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 4px;
        }
        .before {
            background: #fff5f5;
        }
        .after {
            background: #f0fff4;
        }
    </style>
</head>
<body>
    <h1>测试纯文本格式修复</h1>
    
    <div class="test-section">
        <h2>修复说明</h2>
        <p>修复了"生成图片提示词表格"按钮无法正确获取LLM返回内容的问题：</p>
        <ul>
            <li>✅ 修改Google LLM返回纯文本格式，不带HTML标签</li>
            <li>✅ 使用换行符分段，每个内容段落之间用空行分隔</li>
            <li>✅ 前端按行解析纯文本内容</li>
            <li>✅ 支持多种格式的生图提示词匹配</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>格式对比</h2>
        <div class="comparison">
            <div class="before">
                <h3>修复前（HTML格式）</h3>
                <div class="content-display">&lt;h1&gt;《小兔子贝贝的彩虹冒险》&lt;/h1&gt;
&lt;h2&gt;第1页：&lt;/h2&gt;
&lt;p&gt;&lt;strong&gt;主持人角色对白：&lt;/strong&gt;在一个阳光明媚的早晨...&lt;/p&gt;
&lt;p&gt;&lt;strong&gt;生图Prompt详细描述：&lt;/strong&gt;【图片风格为...】&lt;/p&gt;</div>
                <p class="error">❌ 需要HTML解析，容易出错</p>
            </div>
            
            <div class="after">
                <h3>修复后（纯文本格式）</h3>
                <div class="content-display">标题：《小兔子贝贝的彩虹冒险》

第1页：

主持人角色对白：在一个阳光明媚的早晨，小兔子贝贝在花园里发现了一道神奇的彩虹门。

主持人角色配音要求：全篇统一为某个音色，其特色为：成熟稳重、有说服力。

生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小兔子贝贝站在花园中，面前有一道七彩彩虹门，阳光透过云层洒下，花朵盛开，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</div>
                <p class="success">✅ 纯文本，易于解析</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>测试场景：纯文本格式解析</h2>
        <div class="content-display">
            <h3>模拟LLM返回的纯文本内容：</h3>
        </div>
        
        <div class="rich-editor-hidden" data-raw-content="标题：《小兔子贝贝的彩虹冒险》

第1页：

主持人角色对白：在一个阳光明媚的早晨，小兔子贝贝在花园里发现了一道神奇的彩虹门。

主持人角色配音要求：全篇统一为某个音色，其特色为：成熟稳重、有说服力。

生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小兔子贝贝站在花园中，面前有一道七彩彩虹门，阳光透过云层洒下，花朵盛开，中景，特写，超精细，大师构图，背景虚幻，比例 &quot;16:9&quot;】

第2页：

主持人角色对白：贝贝勇敢地踏进了彩虹门，瞬间来到了一个充满魔法的糖果王国。

生图Prompt详细描述：图片风格为3D渲染，色彩鲜艳，小兔子贝贝在糖果王国中，周围有彩色的糖果房屋和棒棒糖树，魔法光芒闪烁，中景，特写，超精细，大师构图，背景虚幻，比例16:9

第3页：

主持人角色对白：在糖果王国里，贝贝遇到了友善的糖果国王。

场景配图，生图Prompt详细描述：【糖果国王坐在棉花糖王座上，身穿巧克力长袍，手持彩虹权杖，小兔子贝贝站在面前，宫殿内装饰着各种甜品，温馨友好的氛围，中景，特写，超精细，大师构图，背景虚幻，比例16:9】">
            <!-- 这里存储原始纯文本内容 -->
        </div>
        
        <div class="llm-functions">
            <button onclick="testGenerateTable()">生成图片提示词表格</button>
        </div>
        <div id="result" class="test-result"></div>
    </div>

    <script>
        function testGenerateTable() {
            console.log('开始测试纯文本格式解析');
            
            // 获取富文本编辑区
            const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
            const latestRichEditor = allRichEditors[allRichEditors.length - 1];
            
            // 优先使用原始纯文本内容
            const rawContent = latestRichEditor ? latestRichEditor.getAttribute('data-raw-content') : '';
            const html = latestRichEditor ? latestRichEditor.innerHTML : '';

            // 添加调试输出
            console.log('获取到的原始文本内容:', rawContent);
            console.log('获取到的HTML内容:', html);
            console.log('富文本编辑区数量:', allRichEditors.length);

            const contentToProcess = rawContent || html;
            if (!contentToProcess) {
                alert('未找到可解析的内容，请先生成故事内容！');
                return;
            }

            // 解析纯文本内容，生成表格（使用修复后的逻辑）
            const prompts = [];
            let pageNum = 1;
            let lastPage = '';
            
            // 按行分割内容
            const lines = contentToProcess.split('\n');
            
            lines.forEach(line => {
                const txt = line.trim();
                console.log('处理行:', txt); // 调试输出
                
                // 提取页数
                let pageMatch = txt.match(/第([一二三四五六七八九十]+)页/);
                if (pageMatch) lastPage = pageMatch[0];
                
                // 改进的生图提示词匹配逻辑，支持多种格式
                let promptText = '';
                
                // 格式1：生图Prompt详细描述：【内容】
                let promptMatch1 = txt.match(/生图Prompt详细描述[：:]\s*[【]([^】]+)[】]/);
                if (promptMatch1) {
                    promptText = promptMatch1[1].trim();
                } else {
                    // 格式2：生图Prompt详细描述：内容（无括号）
                    let promptMatch2 = txt.match(/生图Prompt详细描述[：:]\s*(.+?)(?:\s*$|\s*。|\s*，)/);
                    if (promptMatch2) {
                        promptText = promptMatch2[1].trim();
                    } else {
                        // 格式3：场景配图，生图Prompt详细描述：内容
                        let promptMatch3 = txt.match(/场景配图[，,]\s*生图Prompt详细描述[：:]\s*[【]?([^】\n]+)[】]?/);
                        if (promptMatch3) {
                            promptText = promptMatch3[1].trim();
                        }
                    }
                }
                
                if (promptText) {
                    console.log('找到提示词:', promptText); // 调试输出
                    prompts.push({
                        index: prompts.length + 1,
                        page: lastPage || `第${pageNum}页`,
                        prompt: promptText
                    });
                    pageNum++;
                }
            });
            
            console.log('解析到的提示词数量:', prompts.length); // 调试输出
            
            // 显示结果
            const resultDiv = document.getElementById('result');
            
            if (prompts.length === 0) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h3>❌ 测试失败</h3>
                    <p>未找到生图提示词！请检查内容格式。</p>
                    <p>调试信息已输出到浏览器控制台，请按F12查看。</p>
                `;
                return;
            }
            
            // 生成表格HTML
            let tableHtml = `<table class='prompt-table'>\n`
                + `<thead>\n<tr>\n`
                + `<th>序号</th>`
                + `<th>页数</th>`
                + `<th>生图提示词</th>`
                + `<th>操作</th>`
                + `</tr>\n</thead>\n<tbody>`;
            prompts.forEach(row => {
                tableHtml += `<tr>`
                    + `<td style='text-align:center;'>${row.index}</td>`
                    + `<td style='text-align:center;'>${row.page}</td>`
                    + `<td>${row.prompt}</td>`
                    + `<td style='text-align:center;'>`
                    + `<a href='#' onclick='alert("查看提示词: ${row.prompt.replace(/'/g, "\\'")}"); return false;'>查看</a>`
                    + `</td></tr>`;
            });
            tableHtml += '</tbody></table>';
            
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                <h3>✅ 测试成功！</h3>
                <p><strong>解析到的提示词数量：</strong>${prompts.length}个</p>
                <p><strong>支持的格式：</strong></p>
                <ul>
                    <li>✅ 带【】括号格式：${prompts.filter(p => p.prompt.includes('图片风格为「3D渲染」')).length}个</li>
                    <li>✅ 无括号格式：${prompts.filter(p => !p.prompt.includes('图片风格为「3D渲染」') && p.prompt.includes('图片风格为3D渲染')).length}个</li>
                    <li>✅ 场景配图格式：${prompts.filter(p => p.prompt.includes('糖果国王')).length}个</li>
                </ul>
                <p><strong>生成的表格：</strong></p>
                ${tableHtml}
            `;
            
            console.log('✅ 图片提示词表格生成成功！解析到', prompts.length, '个提示词');
        }
        
        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('纯文本格式修复测试页面加载完成');
            console.log('修复要点：');
            console.log('1. LLM返回纯文本格式，不带HTML标签');
            console.log('2. 使用换行符分段，便于解析');
            console.log('3. 前端按行处理，支持多种提示词格式');
            console.log('4. 优先使用data-raw-content属性中的原始文本');
        };
    </script>
</body>
</html>
