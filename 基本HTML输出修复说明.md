# 基本HTML输出修复说明

## 修复目标
确保Google LLM返回的HTML为基本排版信息，不包含Tailwind CSS等复杂样式，方便后续处理消息内容。

## 问题分析

### 修复前的问题
1. **LLM提示词要求Tailwind样式**：在`start.py`第300行，提示词要求LLM输出包含Tailwind CSS的HTML
2. **后端自动添加样式**：在`start.py`第327-329行，对LLM返回的HTML自动添加了Tailwind样式
3. **影响后续处理**：复杂的CSS类和样式属性影响了内容解析和处理功能

### 修复后的效果
1. **基本HTML输出**：LLM只返回基本HTML标签（h1, h2, p, ul, li, table等）
2. **移除样式属性**：清理所有CSS类、样式属性和ID
3. **前端样式控制**：通过CSS为显示添加样式，不影响存储内容
4. **便于后续处理**：简洁的HTML结构便于解析和处理

## 具体修改

### 1. 修改LLM提示词（start.py 第300行）

**修改前：**
```python
prefix = "请根据中文排版规范，合理美化分段，并用HTML格式，包含tailwindCSS方式完善美化后输出以下完整内容："
```

**修改后：**
```python
prefix = "请根据中文排版规范，合理美化分段，并用基本HTML格式输出以下完整内容。只使用基本的HTML标签如<h1>、<h2>、<p>、<ul>、<li>、<table>、<tr>、<td>、<th>等，不要添加任何CSS类或样式属性："
```

### 2. 移除Tailwind样式处理（start.py 第324-336行）

**修改前：**
```python
if full_text:
    formatted_html = clean_llm_html(full_text)
    tailwind_html = apply_tailwind_styles(formatted_html)  # 添加Tailwind样式
    results.append({
        "type": "text",
        "content": tailwind_html,  # 使用带样式的HTML
        "display_content_once": True
    })
```

**修改后：**
```python
if full_text:
    formatted_html = clean_llm_html(full_text)  # 只清理HTML
    results.append({
        "type": "text",
        "content": formatted_html,  # 直接使用基本HTML
        "raw_content": formatted_html,  # 保存原始内容
        "display_content_once": True
    })
```

### 3. 增强HTML清理函数（start.py clean_llm_html函数）

**新增功能：**
```python
def clean_llm_html(raw_text: str) -> str:
    """
    清理LLM返回的HTML内容，确保返回基本HTML结构。
    优化策略：移除所有CSS类和样式属性，只保留基本HTML标签。
    """
    # ... 原有代码 ...
    
    # 移除所有标签的class和style属性
    for tag in soup.find_all():
        if tag.has_attr('class'):
            del tag['class']
        if tag.has_attr('style'):
            del tag['style']
        # 移除样式相关的属性
        attrs_to_remove = []
        for attr in tag.attrs:
            if attr in ['class', 'style', 'id']:
                attrs_to_remove.append(attr)
        for attr in attrs_to_remove:
            del tag[attr]
```

### 4. 前端添加显示样式（templates/index.html）

**新增CSS样式控制：**
```javascript
// 为基本HTML元素添加样式
rawHtmlDiv.style.cssText = `
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
    margin: 8px 0;
`;

// 为内部HTML元素添加样式
const styleElement = document.createElement('style');
styleElement.textContent = `
    .llm-raw-html h1, .llm-raw-html h2, .llm-raw-html h3 {
        color: #2563eb;
        margin: 16px 0 8px 0;
        font-weight: bold;
    }
    .llm-raw-html p {
        margin: 8px 0;
        line-height: 1.6;
    }
    .llm-raw-html table {
        width: 100%;
        border-collapse: collapse;
        margin: 16px 0;
        background: white;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    // ... 更多样式 ...
`;
```

## 修复效果对比

### HTML输出对比

**修复前（包含Tailwind样式）：**
```html
<h1 class="font-bold text-2xl text-pink-600 mb-4">标题</h1>
<p class="text-lg leading-relaxed text-gray-800 mb-2">段落内容</p>
<table class="min-w-full bg-white border border-gray-300 rounded-lg shadow">
  <th class="bg-blue-200 text-blue-900 px-4 py-2">表头</th>
  <td class="border px-4 py-2">单元格</td>
</table>
```

**修复后（基本HTML）：**
```html
<h1>标题</h1>
<p>段落内容</p>
<table>
  <th>表头</th>
  <td>单元格</td>
</table>
```

### 处理效果对比

| 方面 | 修复前 | 修复后 |
|------|--------|--------|
| HTML复杂度 | 包含大量CSS类 | 基本HTML标签 |
| 解析难度 | 需要处理复杂属性 | 简单直接 |
| 存储大小 | 较大（包含样式） | 较小（纯内容） |
| 后续处理 | 困难（需要清理样式） | 容易（直接解析） |
| 显示效果 | 通过内联样式 | 通过CSS控制 |

## 测试验证

### 验证步骤
1. 启动应用：`python start.py`
2. 生成故事内容
3. 检查浏览器开发者工具中的HTML结构
4. 验证存储的内容是否为基本HTML
5. 测试"生成图片提示词表格"功能是否正常工作

### 预期结果
- ✅ LLM返回的HTML不包含CSS类
- ✅ 显示效果保持美观（通过CSS样式）
- ✅ 后续处理功能正常工作
- ✅ 内容解析更加准确

## 技术要点

1. **分离关注点**：内容存储与显示样式分离
2. **向前兼容**：保持原有显示效果
3. **便于维护**：样式集中在CSS中管理
4. **提高性能**：减少HTML大小，提高解析速度

## 相关文件

- `start.py` - 主要修改文件
- `templates/index.html` - 前端样式修改
- `test_basic_html.html` - 测试验证文件
- `基本HTML输出修复说明.md` - 本说明文档

这个修复确保了LLM返回的HTML为最简单的基本排版信息，方便下一步处理消息内容，同时保持了良好的显示效果。
