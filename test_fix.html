<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复 - 生成图片提示词表格</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .rich-editor-hidden {
            display: none;
        }
        .llm-functions {
            margin-top: 16px;
        }
        button {
            margin: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .gen-prompt-table-btn {
            background: #2563eb;
            color: #fff;
        }
        .prompt-table {
            width: 100%;
            margin-top: 16px;
            border-collapse: collapse;
        }
        .prompt-table th, .prompt-table td {
            border: 1px solid #ddd;
            padding: 6px;
        }
        .prompt-table th {
            background: #f3f4f6;
        }
        .test-result {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
        }
        .success {
            background: #f0fdf4;
            border-color: #22c55e;
        }
    </style>
</head>
<body>
    <h1>测试修复：生成图片提示词表格功能</h1>
    
    <div class="test-section">
        <h2>测试场景1：第一次生成内容</h2>
        <div class="rich-editor-hidden">
            <p>标题：《小兔子贝贝的彩虹冒险》</p>
            <p>第1页：</p>
            <p>主持人角色对白：在一个阳光明媚的早晨，小兔子贝贝在花园里发现了一道神奇的彩虹门。</p>
            <p>生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小兔子贝贝站在花园中，面前有一道七彩彩虹门，阳光透过云层洒下，花朵盛开，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
        </div>
        <div class="llm-functions">
            <button class="gen-prompt-table-btn" onclick="testGenerateTable()">生成图片提示词表格</button>
        </div>
        <div id="result1" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>测试场景2：第二次生成内容（模拟用户再次生成故事）</h2>
        <div class="rich-editor-hidden">
            <p>标题：《勇敢的小猫咪米米》</p>
            <p>第1页：</p>
            <p>主持人角色对白：小猫咪米米住在一个美丽的小镇上，她最喜欢探索新的地方。</p>
            <p>生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小猫咪米米在小镇街道上，周围有彩色房屋和绿树，阳光明媚，全景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
            <p>第2页：</p>
            <p>主持人角色对白：有一天，米米听说森林深处有一个神秘的宝藏。</p>
            <p>生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小猫咪米米站在森林入口，古老的树木环绕，神秘的光芒从深处传来，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
        </div>
        <div class="llm-functions">
            <!-- 这里不创建新按钮，测试现有按钮是否能获取最新内容 -->
        </div>
        <div id="result2" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>测试场景3：第三次生成内容（再次测试）</h2>
        <div class="rich-editor-hidden">
            <p>标题：《神奇的魔法师小明》</p>
            <p>第1页：</p>
            <p>主持人角色对白：小明是一个年轻的魔法师，他正在学习各种神奇的魔法。</p>
            <p>生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，年轻魔法师小明手持魔法棒，身穿蓝色法袍，周围有闪闪发光的魔法粒子，魔法塔背景，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
            <p>第2页：</p>
            <p>主持人角色对白：今天，小明要学习一个新的变身魔法。</p>
            <p>生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小明在魔法教室里，面前有古老的魔法书，魔法符文在空中飞舞，神秘的光芒环绕，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
            <p>第3页：</p>
            <p>主持人角色对白：小明成功施展了魔法，变成了一只可爱的小鸟。</p>
            <p>生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小明变成的小鸟在天空中飞翔，下方是美丽的城镇，云朵飘浮，阳光灿烂，俯视，特写，超精细，大师构图，背景虚幻，比例 "16:9"】</p>
        </div>
        <div class="llm-functions">
            <!-- 这里也不创建新按钮，继续测试现有按钮 -->
        </div>
        <div id="result3" class="test-result"></div>
    </div>

    <script>
        function testGenerateTable() {
            // 模拟修复后的逻辑：动态获取最新的富文本编辑区内容
            const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
            const latestRichEditor = allRichEditors[allRichEditors.length - 1];
            const html = latestRichEditor ? latestRichEditor.innerHTML : '';
            
            if (!html) {
                alert('未找到可解析的内容，请先生成故事内容！');
                return;
            }
            
            // 解析HTML，生成表格
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            const prompts = [];
            let pageNum = 1;
            let lastPage = '';
            
            tempDiv.querySelectorAll('p').forEach(p => {
                const txt = p.textContent || '';
                // 提取页数
                let pageMatch = txt.match(/第([一二三四五六七八九十]+)页/);
                if (pageMatch) lastPage = pageMatch[0];
                // 提取生图Prompt详细描述括号内内容
                let promptMatch = txt.match(/生图Prompt详细描述[：:][^【]*([【][^】]+[】])/);
                if (promptMatch) {
                    // 只取第一个【】内内容
                    let promptText = promptMatch[1].replace(/[【】]/g, '');
                    prompts.push({
                        index: prompts.length + 1,
                        page: lastPage || `第${pageNum}页`,
                        prompt: promptText.trim()
                    });
                    pageNum++;
                }
            });
            
            // 生成表格HTML
            let tableHtml = `<table class='prompt-table'>\n`
                + `<thead>\n<tr>\n`
                + `<th>序号</th>`
                + `<th>页数</th>`
                + `<th>生图提示词</th>`
                + `<th>操作</th>`
                + `</tr>\n</thead>\n<tbody>`;
            
            prompts.forEach(row => {
                tableHtml += `<tr>`
                    + `<td style='text-align:center;'>${row.index}</td>`
                    + `<td style='text-align:center;'>${row.page}</td>`
                    + `<td>${row.prompt}</td>`
                    + `<td style='text-align:center;'>`
                    + `<a href='#' onclick='alert("查看提示词: ${row.prompt}")'>查看</a>`
                    + `</td></tr>`;
            });
            tableHtml += '</tbody></table>';
            
            // 显示结果
            const resultDiv = document.getElementById('result1');
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `
                <h3>✅ 测试成功！</h3>
                <p><strong>获取到的内容来源：</strong>第${allRichEditors.length}个富文本编辑区</p>
                <p><strong>解析到的提示词数量：</strong>${prompts.length}个</p>
                <p><strong>提示词内容：</strong></p>
                <ul>
                    ${prompts.map(p => `<li>${p.page}: ${p.prompt.substring(0, 50)}...</li>`).join('')}
                </ul>
                ${tableHtml}
            `;
            
            // 同时更新其他测试结果
            updateTestResults(allRichEditors.length, prompts);
        }
        
        function updateTestResults(totalEditors, prompts) {
            // 更新测试场景2的结果
            const result2 = document.getElementById('result2');
            if (totalEditors >= 2) {
                result2.className = 'test-result success';
                result2.innerHTML = `
                    <h3>✅ 场景2测试通过</h3>
                    <p>按钮能够正确获取到最新（第${totalEditors}个）富文本编辑区的内容，而不是第1个的内容。</p>
                `;
            } else {
                result2.className = 'test-result';
                result2.innerHTML = `<p>等待测试...</p>`;
            }
            
            // 更新测试场景3的结果
            const result3 = document.getElementById('result3');
            if (totalEditors >= 3) {
                result3.className = 'test-result success';
                result3.innerHTML = `
                    <h3>✅ 场景3测试通过</h3>
                    <p>按钮能够正确获取到最新（第${totalEditors}个）富文本编辑区的内容，解析到${prompts.length}个提示词。</p>
                    <p><strong>这证明修复成功：</strong>按钮现在能动态获取最新内容，而不是固定引用第一次的内容。</p>
                `;
            } else {
                result3.className = 'test-result';
                result3.innerHTML = `<p>等待测试...</p>`;
            }
        }
        
        // 页面加载完成后显示说明
        window.onload = function() {
            document.getElementById('result1').innerHTML = `
                <p><strong>测试说明：</strong></p>
                <p>1. 点击"生成图片提示词表格"按钮</p>
                <p>2. 按钮会动态获取最新的富文本编辑区内容</p>
                <p>3. 如果修复成功，按钮应该能获取到第3个编辑区的内容（包含3个提示词）</p>
                <p>4. 如果修复失败，按钮只能获取到第1个编辑区的内容（只有1个提示词）</p>
            `;
            
            document.getElementById('result2').innerHTML = `<p>等待测试...</p>`;
            document.getElementById('result3').innerHTML = `<p>等待测试...</p>`;
        };
    </script>
</body>
</html>
