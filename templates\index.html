<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini 绘本生成器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <script src="{{ url_for('static', filename='js/table_logger.js') }}"></script>
</head>
<body>
    <div class="container">
        <h1>Gemini 绘本生成器</h1>

        <div class="form-container">
            <form id="promptForm">
                <div class="model-selection">
                    <label>选择模型:</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="model" value="{{ gemini_flash_model }}" checked>
                            {{ gemini_flash_model }}
                        </label>
                        <label>
                            <input type="radio" name="model" value="gemini-2.0-flash-exp-image-generation">
                            gemini-2.0-flash-exp-image-generation
                        </label>
                    </div>
                </div>
                <div>
                    <label for="prompt">输入提示词:</label>
                    <div id="prompt" name="prompt" contenteditable="true" class="rich-text-editor" style="min-height:120px;border:1px solid #ccc;padding:8px;border-radius:4px;background:#fff;outline:none;" required>{{ default_prompt|safe }}</div>
                </div>

                <div class="button-container">
                    <button type="submit" id="generateBtn" class="generate-btn">生成绘本</button>
                    <button type="button" id="testApiBtn" class="test-btn">测试API连接</button>
                    <button type="button" id="exportBtn" class="export-btn">导出故事</button>
                </div>

                <div id="apiInfo">使用API密钥: {{ api_key }}</div>
            </form>

            <div id="statusMessage" class="status"></div>
        </div>

        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <p>正在处理请求，请稍候...</p>
        </div>

        <div class="result-container" id="resultContainer" style="display: none;">
            <h2>生成结果</h2>
            <div id="results"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('promptForm');
            const generateBtn = document.getElementById('generateBtn');
            const testApiBtn = document.getElementById('testApiBtn');
            const promptInput = document.getElementById('prompt');
            const results = document.getElementById('results');
            const resultContainer = document.getElementById('resultContainer');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statusMessage = document.getElementById('statusMessage');
            const apiInfo = document.getElementById('apiInfo');
            const exportBtn = document.getElementById('exportBtn');

            // 生成绘本
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('生成按钮被点击');

                const prompt = promptInput.innerHTML.trim();
                if (!prompt) {
                    console.log('提示词为空，终止请求');
                    return;
                }

                // 重置UI
                results.innerHTML = '';
                resultContainer.style.display = 'none';
                statusMessage.className = 'status';
                statusMessage.style.display = 'none';
                loadingIndicator.style.display = 'block';
                generateBtn.disabled = true;

                try {
                    // 获取选中的模型
                    const selectedModel = document.querySelector('input[name="model"]:checked').value;
                    console.log('发送提示词:', prompt, '模型:', selectedModel);
                    const jsonData = JSON.stringify({ prompt: prompt, model: selectedModel });
                    console.log('JSON数据:', jsonData);

                    console.log('开始发送网络请求...');
                    const response = await fetch('/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: jsonData
                    });
                    console.log('网络请求已发送，状态码:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }

                    let data;
                    try {
                        data = await response.json();
                        console.log('收到API响应:', data);
                    } catch (jsonError) {
                        console.error('JSON解析错误:', jsonError);
                        throw new Error('无法将响应解析为JSON');
                    }

                    // 显示结果
                    resultContainer.style.display = 'block';

                    // 处理不同的状态类型
                    if (data.status === 'success' || data.status === 'warning') {
                        if (data.status === 'success') {
                            statusMessage.className = 'status success';
                            statusMessage.textContent = '生成成功！';
                        } else {
                            statusMessage.className = 'status warning';
                            statusMessage.textContent = `警告: ${data.message || '部分内容可能未正确生成'}`;
                        }
                        statusMessage.style.display = 'block';

                        // 处理API密钥信息
                        if (data.api_key) {
                            apiInfo.textContent = `使用API密钥: ${data.api_key}`;
                        }

                        // 处理结果
                        if (data.results && data.results.length > 0) {
                            data.results.forEach((item, index) => {
                                console.log(`处理结果项 ${index}:`, item);

                                // 为此结果创建容器
                                const resultItem = document.createElement('div');
                                resultItem.className = 'result-item';

                                // 处理图像
                                if (item.type === 'image' && item.data) {
                                    const imageContainer = document.createElement('div');
                                    imageContainer.className = 'image-container';

                                    try {
                                        const img = document.createElement('img');
                                        img.src = item.data;
                                        img.alt = `生成的图片 #${index+1}`;
                                        img.className = 'generated-image';
                                        img.onerror = function() {
                                            imageContainer.innerHTML = '<p class="error-message">图片加载失败</p>';
                                        };
                                        imageContainer.appendChild(img);
                                    } catch (imgError) {
                                        console.error('图片加载错误:', imgError);
                                        imageContainer.innerHTML = '<p class="error-message">图片处理失败</p>';
                                    }

                                    resultItem.appendChild(imageContainer);
                                }

                                // 处理文本
                                if (item.type === 'text' && item.content) {
                                    // 1. 创建LLM原始HTML显示容器，确保完整显示
                                    const llmContentWrapper = document.createElement('div');
                                    llmContentWrapper.className = 'llm-content-wrapper';

                                    // 2. 原始HTML内容显示区域，添加基本样式
                                    const rawHtmlDiv = document.createElement('div');
                                    rawHtmlDiv.className = 'llm-raw-html';
                                    rawHtmlDiv.innerHTML = item.content;

                                    // 为基本HTML元素添加样式
                                    rawHtmlDiv.style.cssText = `
                                        font-family: Arial, sans-serif;
                                        line-height: 1.6;
                                        color: #333;
                                        padding: 16px;
                                        background: #f9f9f9;
                                        border-radius: 8px;
                                        margin: 8px 0;
                                    `;

                                    // 为内部HTML元素添加样式
                                    const styleElement = document.createElement('style');
                                    styleElement.textContent = `
                                        .llm-raw-html h1, .llm-raw-html h2, .llm-raw-html h3 {
                                            color: #2563eb;
                                            margin: 16px 0 8px 0;
                                            font-weight: bold;
                                        }
                                        .llm-raw-html h1 { font-size: 24px; }
                                        .llm-raw-html h2 { font-size: 20px; }
                                        .llm-raw-html h3 { font-size: 18px; }
                                        .llm-raw-html p {
                                            margin: 8px 0;
                                            line-height: 1.6;
                                        }
                                        .llm-raw-html ul, .llm-raw-html ol {
                                            margin: 8px 0;
                                            padding-left: 24px;
                                        }
                                        .llm-raw-html li {
                                            margin: 4px 0;
                                        }
                                        .llm-raw-html table {
                                            width: 100%;
                                            border-collapse: collapse;
                                            margin: 16px 0;
                                            background: white;
                                            border-radius: 4px;
                                            overflow: hidden;
                                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                        }
                                        .llm-raw-html th {
                                            background: #e5e7eb;
                                            color: #374151;
                                            padding: 12px;
                                            text-align: left;
                                            font-weight: bold;
                                        }
                                        .llm-raw-html td {
                                            padding: 12px;
                                            border-bottom: 1px solid #e5e7eb;
                                        }
                                        .llm-raw-html tr:hover {
                                            background: #f9fafb;
                                        }
                                    `;
                                    document.head.appendChild(styleElement);

                                    llmContentWrapper.appendChild(rawHtmlDiv);

                                    // 3. 功能按钮区域
                                    const functionsDiv = document.createElement('div');
                                    functionsDiv.className = 'llm-functions';

                                    // 4. 隐藏的富文本编辑区（用于后续功能处理）
                                    const richEditorDiv = document.createElement('div');
                                    richEditorDiv.className = 'rich-editor-hidden';
                                    richEditorDiv.style.display = 'none';
                                    // 存储原始纯文本内容用于解析
                                    richEditorDiv.setAttribute('data-raw-content', item.raw_content || '');
                                    richEditorDiv.innerHTML = item.content;

                                    // 5. 组装结构
                                    resultItem.appendChild(llmContentWrapper);
                                    resultItem.appendChild(functionsDiv);
                                    resultItem.appendChild(richEditorDiv);
                                    results.appendChild(resultItem);

                                    // 6. 插入"生成图片提示词表格"按钮到功能区域
                                    if (!document.querySelector('.gen-prompt-table-btn')) {
                                        const genPromptTableBtn = document.createElement('button');
                                        genPromptTableBtn.className = 'gen-prompt-table-btn';
                                        genPromptTableBtn.textContent = '生成图片提示词表格';
                                        genPromptTableBtn.style.marginTop = '16px';
                                        genPromptTableBtn.style.background = '#2563eb';
                                        genPromptTableBtn.style.color = '#fff';
                                        genPromptTableBtn.style.border = 'none';
                                        genPromptTableBtn.style.padding = '8px 16px';
                                        genPromptTableBtn.style.borderRadius = '6px';
                                        genPromptTableBtn.style.cursor = 'pointer';
                                        functionsDiv.appendChild(genPromptTableBtn);

                                        genPromptTableBtn.onclick = function() {
                                            // 动态获取最新的富文本编辑区内容
                                            const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
                                            const latestRichEditor = allRichEditors[allRichEditors.length - 1];

                                            // 优先使用原始纯文本内容
                                            const rawContent = latestRichEditor ? latestRichEditor.getAttribute('data-raw-content') : '';
                                            const html = latestRichEditor ? latestRichEditor.innerHTML : '';

                                            // 添加调试输出
                                            console.log('获取到的原始文本内容:', rawContent);
                                            console.log('获取到的HTML内容:', html);
                                            console.log('富文本编辑区数量:', allRichEditors.length);

                                            const contentToProcess = rawContent || html;
                                            if (!contentToProcess) {
                                                alert('未找到可解析的内容，请先生成故事内容！');
                                                return;
                                            }

                                            // 解析纯文本内容，生成表格
                                            const prompts = [];
                                            let pageNum = 1;
                                            let lastPage = '';

                                            // 按行分割内容
                                            const lines = contentToProcess.split('\n');

                                            lines.forEach(line => {
                                                const txt = line.trim();
                                                console.log('处理行:', txt); // 调试输出

                                                // 提取页数
                                                let pageMatch = txt.match(/第([一二三四五六七八九十]+)页/);
                                                if (pageMatch) lastPage = pageMatch[0];

                                                // 改进的生图提示词匹配逻辑，支持多种格式
                                                let promptText = '';

                                                // 格式1：生图Prompt详细描述：【内容】
                                                let promptMatch1 = txt.match(/生图Prompt详细描述[：:]\s*[【]([^】]+)[】]/);
                                                if (promptMatch1) {
                                                    promptText = promptMatch1[1].trim();
                                                } else {
                                                    // 格式2：生图Prompt详细描述：内容（无括号）
                                                    let promptMatch2 = txt.match(/生图Prompt详细描述[：:]\s*(.+?)(?:\s*$|\s*。|\s*，)/);
                                                    if (promptMatch2) {
                                                        promptText = promptMatch2[1].trim();
                                                    } else {
                                                        // 格式3：场景配图，生图Prompt详细描述：内容
                                                        let promptMatch3 = txt.match(/场景配图[，,]\s*生图Prompt详细描述[：:]\s*[【]?([^】\n]+)[】]?/);
                                                        if (promptMatch3) {
                                                            promptText = promptMatch3[1].trim();
                                                        }
                                                    }
                                                }

                                                if (promptText) {
                                                    console.log('找到提示词:', promptText); // 调试输出
                                                    prompts.push({
                                                        index: prompts.length + 1,
                                                        page: lastPage || `第${pageNum}页`,
                                                        prompt: promptText
                                                    });
                                                    pageNum++;
                                                }
                                            });

                                            console.log('解析到的提示词数量:', prompts.length); // 调试输出

                                            // 检查是否找到提示词
                                            if (prompts.length === 0) {
                                                alert('未找到生图提示词！\n\n可能的原因：\n1. 内容中没有包含"生图Prompt详细描述"字样\n2. 格式不正确\n\n请检查生成的内容是否包含生图提示词。\n\n调试信息已输出到浏览器控制台，请按F12查看。');
                                                return;
                                            }

                                            // 生成表格HTML
                                            let tableHtml = `<table class='prompt-table' style='width:100%;margin-top:16px;border-collapse:collapse;'>\n`
                                                + `<thead>\n<tr style='background:#f3f4f6;'>\n`
                                                + `<th style='border:1px solid #ddd;padding:6px;'>序号</th>`
                                                + `<th style='border:1px solid #ddd;padding:6px;'>页数</th>`
                                                + `<th style='border:1px solid #ddd;padding:6px;'>生图提示词</th>`
                                                + `<th style='border:1px solid #ddd;padding:6px;'>操作</th>`
                                                + `</tr>\n</thead>\n<tbody>`;
                                            prompts.forEach(row => {
                                                tableHtml += `<tr>`
                                                    + `<td style='border:1px solid #ddd;padding:6px;text-align:center;'>${row.index}</td>`
                                                    + `<td style='border:1px solid #ddd;padding:6px;text-align:center;'>${row.page}</td>`
                                                    + `<td style='border:1px solid #ddd;padding:6px;'>${row.prompt}</td>`
                                                    + `<td style='border:1px solid #ddd;padding:6px;text-align:center;'>`
                                                    + `<a href='#' class='view-prompt-link' data-index='${row.index-1}'>查看</a>`
                                                    + `</td></tr>`;
                                            });
                                            tableHtml += '</tbody></table>';

                                            // 移除旧表格
                                            let oldTable = functionsDiv.querySelector('.prompt-table');
                                            if (oldTable) oldTable.remove();

                                            const tableDiv = document.createElement('div');
                                            tableDiv.innerHTML = tableHtml;
                                            functionsDiv.appendChild(tableDiv);

                                            // 显示成功消息
                                            console.log('✅ 图片提示词表格生成成功！解析到', prompts.length, '个提示词');

                                            // 弹窗编辑
                                            tableDiv.querySelectorAll('.view-prompt-link').forEach(link => {
                                                link.onclick = function(e) {
                                                    e.preventDefault();
                                                    const idx = parseInt(link.getAttribute('data-index'));
                                                    showPromptModal(prompts, idx, tableDiv);
                                                };
                                            });

                                            // 动态插入"配音表生成"按钮（仅插入一次，且表格加载后才出现）
                                            if (!functionsDiv.querySelector('.next-step-btn')) {
                                                const nextStepBtn = document.createElement('button');
                                                nextStepBtn.className = 'next-step-btn';
                                                nextStepBtn.textContent = '配音表生成';
                                                nextStepBtn.style.marginTop = '16px';
                                                nextStepBtn.style.background = '#10b981';
                                                nextStepBtn.style.color = '#fff';
                                                nextStepBtn.style.border = 'none';
                                                nextStepBtn.style.padding = '8px 16px';
                                                nextStepBtn.style.borderRadius = '6px';
                                                nextStepBtn.style.cursor = 'pointer';
                                                functionsDiv.appendChild(nextStepBtn);
                                                // 保持后续逻辑不变
                                                nextStepBtn.onclick = async function() {
                                                    nextStepBtn.disabled = true;
                                                    nextStepBtn.textContent = '正在生成配音表...';
                                                    // 动态获取最新的富文本编辑区内容
                                                    const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
                                                    const latestRichEditor = allRichEditors[allRichEditors.length - 1];

                                                    // 优先使用原始纯文本内容
                                                    const rawContent = latestRichEditor ? latestRichEditor.getAttribute('data-raw-content') : '';
                                                    const htmlContent = rawContent || (latestRichEditor ? latestRichEditor.innerHTML : '');

                                                    if (!htmlContent) {
                                                        alert('未找到可解析的内容，请先生成故事内容！');
                                                        nextStepBtn.disabled = false;
                                                        nextStepBtn.textContent = '配音表生成';
                                                        return;
                                                    }
                                                    try {
                                                        const response = await fetch('/generate_speech_table', {
                                                            method: 'POST',
                                                            headers: { 'Content-Type': 'application/json' },
                                                            body: JSON.stringify({ html_text: htmlContent })
                                                        });
                                                        const data = await response.json();
                                                        if (data.status === 'success') {
                                                            const tableDiv2 = document.createElement('div');
                                                            tableDiv2.className = 'table-container';
                                                            tableDiv2.innerHTML = data.table_html;
                                                            functionsDiv.appendChild(tableDiv2);
                                                            nextStepBtn.textContent = '配音表已生成';
                                                            // 在生成配音表按钮生成配音表成功后，动态插入"SRT配音表生成"按钮
                                                            if (!functionsDiv.querySelector('.next-srt-btn')) {
                                                                const nextSrtBtn = document.createElement('button');
                                                                nextSrtBtn.className = 'next-srt-btn';
                                                                nextSrtBtn.textContent = 'SRT配音表生成';
                                                                nextSrtBtn.style.marginTop = '16px';
                                                                nextSrtBtn.style.background = '#f59e42';
                                                                nextSrtBtn.style.color = '#fff';
                                                                nextSrtBtn.style.border = 'none';
                                                                nextSrtBtn.style.padding = '8px 16px';
                                                                nextSrtBtn.style.borderRadius = '6px';
                                                                nextSrtBtn.style.cursor = 'pointer';
                                                                functionsDiv.appendChild(nextSrtBtn);
                                                                nextSrtBtn.onclick = async function() {
                                                                    nextSrtBtn.disabled = true;
                                                                    nextSrtBtn.textContent = '正在生成SRT及配音表...';

                                                                    // 动态获取最新的富文本编辑区内容
                                                                    const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
                                                                    const latestRichEditor = allRichEditors[allRichEditors.length - 1];

                                                                    // 优先使用原始纯文本内容
                                                                    const rawContent = latestRichEditor ? latestRichEditor.getAttribute('data-raw-content') : '';
                                                                    const currentHtmlContent = rawContent || (latestRichEditor ? latestRichEditor.innerHTML : '');

                                                                    if (!currentHtmlContent) {
                                                                        alert('未找到可解析的内容，请先生成故事内容！');
                                                                        nextSrtBtn.disabled = false;
                                                                        nextSrtBtn.textContent = 'SRT配音表生成';
                                                                        return;
                                                                    }

                                                                    try {
                                                                        const srtResp = await fetch('/generate_srt_files', {
                                                                            method: 'POST',
                                                                            headers: { 'Content-Type': 'application/json' },
                                                                            body: JSON.stringify({ html_text: currentHtmlContent })
                                                                        });
                                                                        const srtData = await srtResp.json();
                                                                        if (srtData.status === 'success') {
                                                                            const srtTableDiv = document.createElement('div');
                                                                            srtTableDiv.className = 'table-container';
                                                                            srtTableDiv.innerHTML = srtData.table_html;
                                                                            functionsDiv.appendChild(srtTableDiv);
                                                                            nextSrtBtn.textContent = '配音SRT表已生成';
                                                                            bindSrtModalEvents(srtTableDiv);
                                                                        } else {
                                                                            nextSrtBtn.disabled = false;
                                                                            nextSrtBtn.textContent = '生成失败，请重试';
                                                                            alert(srtData.message || '生成SRT配音表失败');
                                                                        }
                                                                    } catch (err) {
                                                                        nextSrtBtn.disabled = false;
                                                                        nextSrtBtn.textContent = '生成失败，请重试';
                                                                        alert('网络错误或服务器异常');
                                                                    }
                                                                };
                                                            }
                                                        } else {
                                                            nextStepBtn.disabled = false;
                                                            nextStepBtn.textContent = '生成失败，请重试';
                                                            alert(data.message || '生成配音表失败');
                                                        }
                                                    } catch (err) {
                                                        nextStepBtn.disabled = false;
                                                        nextStepBtn.textContent = '生成失败，请重试';
                                                        alert('网络错误或服务器异常');
                                                    }
                                                };
                                            }
                                        };
                                    }
                                }
                                const textContainer = document.createElement('div');
                                textContainer.className = 'text-container';

                                // 添加元数据显示
                                if (item.metadata) {
                                    // 创建元数据容器
                                    const metadataContainer = document.createElement('div');
                                    metadataContainer.className = 'message-metadata';

                                    // 添加角色标识
                                    if (item.metadata.role) {
                                        const roleSpan = document.createElement('span');
                                        roleSpan.className = `role-badge role-${item.metadata.role}`;
                                        roleSpan.textContent = item.metadata.role === 'assistant' ? 'AI' : item.metadata.role;
                                        metadataContainer.appendChild(roleSpan);
                                    }

                                    // 添加序列号
                                    if (item.metadata.sequence) {
                                        const sequenceSpan = document.createElement('span');
                                        sequenceSpan.className = 'sequence-number';
                                        sequenceSpan.textContent = `#${item.metadata.sequence}`;
                                        metadataContainer.appendChild(sequenceSpan);
                                    }

                                    // 添加时间戳
                                    if (item.metadata.timestamp) {
                                        const timeSpan = document.createElement('span');
                                        timeSpan.className = 'timestamp';
                                        timeSpan.textContent = item.metadata.timestamp;
                                        metadataContainer.appendChild(timeSpan);
                                    }

                                    // 如果是对话内容，添加对话标记
                                    if (item.metadata.is_dialogue) {
                                        textContainer.classList.add('dialogue-content');

                                        const dialogueIcon = document.createElement('span');
                                        dialogueIcon.className = 'content-type-icon dialogue-icon';
                                        dialogueIcon.title = '包含对话内容';
                                        dialogueIcon.innerHTML = '<i class="icon">💬</i>';
                                        metadataContainer.appendChild(dialogueIcon);
                                    }

                                    // 添加内容类型指示器
                                    if (item.metadata.content_type) {
                                        const contentTypeContainer = document.createElement('div');
                                        contentTypeContainer.className = 'content-type-container';

                                        if (item.metadata.content_type.has_story) {
                                            const storyIcon = document.createElement('span');
                                            storyIcon.className = 'content-type-icon story-icon';
                                            storyIcon.title = '包含故事内容';
                                            storyIcon.innerHTML = '<i class="icon">📖</i>';
                                            contentTypeContainer.appendChild(storyIcon);
                                        }

                                        if (item.metadata.content_type.has_prompt) {
                                            const promptIcon = document.createElement('span');
                                            promptIcon.className = 'content-type-icon prompt-icon';
                                            promptIcon.title = '包含生图提示词';
                                            promptIcon.innerHTML = '<i class="icon">🎨</i>';
                                            contentTypeContainer.appendChild(promptIcon);
                                        }

                                        if (contentTypeContainer.children.length > 0) {
                                            metadataContainer.appendChild(contentTypeContainer);
                                        }
                                    }

                                    // 添加情感标签
                                    if (item.metadata.emotions && item.metadata.emotions.length > 0) {
                                        const emotionsContainer = document.createElement('div');
                                        emotionsContainer.className = 'emotions-container';

                                        item.metadata.emotions.forEach(emotion => {
                                            const emotionTag = document.createElement('span');
                                            emotionTag.className = `emotion-tag emotion-${emotion}`;
                                            emotionTag.textContent = emotion;
                                            emotionsContainer.appendChild(emotionTag);
                                        });

                                        metadataContainer.appendChild(emotionsContainer);
                                    }

                                    // 添加主题标签
                                    if (item.metadata.themes && item.metadata.themes.length > 0) {
                                        const themesContainer = document.createElement('div');
                                        themesContainer.className = 'themes-container';

                                        item.metadata.themes.forEach(theme => {
                                            const themeTag = document.createElement('span');
                                            themeTag.className = 'theme-tag';
                                            themeTag.textContent = theme;
                                            themesContainer.appendChild(themeTag);
                                        });

                                        metadataContainer.appendChild(themesContainer);
                                    }

                                    // 添加统计信息按钮
                                    if (item.metadata.statistics) {
                                        const statsButton = document.createElement('button');
                                        statsButton.className = 'stats-button';
                                        statsButton.textContent = '统计';
                                        statsButton.title = '显示文本统计信息';

                                        statsButton.addEventListener('click', function() {
                                            const stats = item.metadata.statistics;
                                            alert(`文本统计信息:\n字符数: ${stats.char_count}\n词数: ${stats.word_count}\n句子数: ${stats.sentence_count}`);
                                        });

                                        metadataContainer.appendChild(statsButton);
                                    }

                                    // 添加原始文本查看按钮
                                    if (item.raw_text) {
                                        const rawTextButton = document.createElement('button');
                                        rawTextButton.className = 'raw-text-button';
                                        rawTextButton.textContent = '原文';
                                        rawTextButton.title = '查看原始未格式化文本';

                                        rawTextButton.addEventListener('click', function() {
                                            // 创建模态框显示原始文本
                                            const modal = document.createElement('div');
                                            modal.className = 'modal';

                                            const modalContent = document.createElement('div');
                                            modalContent.className = 'modal-content';

                                            const closeBtn = document.createElement('span');
                                            closeBtn.className = 'close-button';
                                            closeBtn.innerHTML = '&times;';
                                            closeBtn.onclick = function() {
                                                document.body.removeChild(modal);
                                            };

                                            const title = document.createElement('h3');
                                            title.textContent = '原始未格式化文本';

                                            const content = document.createElement('pre');
                                            content.className = 'raw-text';
                                            content.textContent = item.raw_text;

                                            modalContent.appendChild(closeBtn);
                                            modalContent.appendChild(title);
                                            modalContent.appendChild(content);
                                            modal.appendChild(modalContent);

                                            // 点击模态框外部关闭
                                            modal.onclick = function(event) {
                                                if (event.target === modal) {
                                                    document.body.removeChild(modal);
                                                }
                                            };

                                            document.body.appendChild(modal);
                                        });

                                        metadataContainer.appendChild(rawTextButton);
                                    }

                                    textContainer.appendChild(metadataContainer);
                                }
                            });
                        } else {
                            results.innerHTML = '<p>未返回任何结果。</p>';
                        }
                    } else {
                        // 处理错误状态
                        statusMessage.className = 'status error';
                        statusMessage.textContent = data.message || '生成过程中发生错误';
                        statusMessage.style.display = 'block';

                        if (data.error_details) {
                            const errorDetails = document.createElement('div');
                            errorDetails.className = 'error-details';
                            errorDetails.textContent = data.error_details;
                            results.appendChild(errorDetails);
                        }
                    }
                } catch (error) {
                    console.error('请求错误:', error);
                    statusMessage.className = 'status error';
                    statusMessage.textContent = error.message || '无法与服务器通信';
                    statusMessage.style.display = 'block';
                } finally {
                    loadingIndicator.style.display = 'none';
                    generateBtn.disabled = false;
                }
            });

            // 测试API连接
            testApiBtn.addEventListener('click', async function() {
                console.log('测试API按钮被点击');
                testApiBtn.disabled = true;
                apiInfo.textContent = '正在测试API连接...';

                try {
                    console.log('开始发送测试API请求...');
                    const response = await fetch(`/test_api?model=${encodeURIComponent('{{ gemini_flash_model }}')}`, { method: 'GET' });
                    console.log('测试API请求已发送，状态码:', response.status);
                    const data = await response.json();
                    console.log('测试API响应数据:', data);

                    if (data.status === 'success') {
                        apiInfo.textContent = `API连接成功: ${data.test_text || 'OK'}`;
                    } else {
                        apiInfo.textContent = `API测试失败: ${data.message}`;
                    }
                } catch (error) {
                    console.error('测试API请求错误:', error);
                    apiInfo.textContent = '测试API连接时出错';
                } finally {
                    testApiBtn.disabled = false;
                }
            });

            // 导出故事按钮逻辑
            exportBtn.addEventListener('click', async function() {
                console.log('导出故事按钮被点击');
                // 获取当前生成的内容
                const storyTitle = (promptInput.textContent || promptInput.innerText || "").trim().split('\n')[0] || '故事';
                const defaultFileName = storyTitle.replace(/[^\u4e00-\u9fa5\w]/g, '') + '.docx';
                // 收集所有生成的文本和图片
                const resultItems = [];
                document.querySelectorAll('#results .result-item').forEach(item => {
                    // 文本
                    const textDiv = item.querySelector('.text-container');
                    if (textDiv) {
                        // 去除HTML标签，保留纯文本
                        const text = textDiv.textContent || '';
                        if (text.trim()) {
                            resultItems.push({type: 'text', content: text.trim()});
                        }
                    }
                    // 图片
                    const img = item.querySelector('img');
                    if (img && img.src && img.src.startsWith('data:image/')) {
                        resultItems.push({type: 'image', data: img.src});
                    }
                });
                if (resultItems.length === 0) {
                    alert('没有可导出的内容，请先生成故事！');
                    return;
                }
                exportBtn.disabled = true;
                exportBtn.textContent = '正在导出...';
                try {
                    const response = await fetch('/export', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ title: storyTitle, results: resultItems })
                    });
                    if (!response.ok) {
                        throw new Error('导出失败: ' + response.status);
                    }
                    const blob = await response.blob();
                    // 处理后端返回的错误信息
                    if (blob.type.indexOf('application/json') !== -1) {
                        const reader = new FileReader();
                        reader.onload = function() {
                            const err = JSON.parse(reader.result);
                            alert('导出失败: ' + (err.message || '未知错误'));
                        };
                        reader.readAsText(blob);
                        return;
                    }
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = defaultFileName;
                    document.body.appendChild(a);
                    a.click();
                    setTimeout(() => {
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    }, 100);
                } catch (err) {
                    alert('导出失败: ' + (err.message || err));
                } finally {
                    exportBtn.disabled = false;
                    exportBtn.textContent = '导出故事';
                }
            });

            // SRT表格弹窗功能
            function bindSrtModalEvents(container) {
                const srtLinks = container.querySelectorAll('a[href^="/srt_temp/"]');
                srtLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const url = link.getAttribute('href');
                        const filename = url.split('/').pop();

                        // 打开SRT编辑模板页面
                        const width = 800;
                        const height = 600;
                        const left = (window.innerWidth - width) / 2;
                        const top = (window.innerHeight - height) / 2;

                        const newWindow = window.open(
                            `/templates/srt_edit_template.html?file=${filename}`,
                            'srt_editor',
                            `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`
                        );

                        // 监听从编辑窗口返回的消息
                        window.addEventListener('message', function(event) {
                            if (event.data && event.data.type === 'srt_updated') {
                                // 可以在这里进行刷新或其他操作
                                console.log('SRT文件已更新:', event.data.filename);
                            }
                        });
                    });
                });
            }

            // 在表格初始化时添加日志记录
            window.tableLogger.logOperation('table_init', -1, 'all', '表格初始化');

            // 监听表格单元格编辑
            document.addEventListener('cellEdited', function(e) {
                window.tableLogger.logCellEdit(
                    e.detail.row,
                    e.detail.column,
                    e.detail.newValue,
                    e.detail.oldValue
                );
            });

            // 监听表格行删除
            document.addEventListener('rowDeleted', function(e) {
                window.tableLogger.logRowDelete(e.detail.row, e.detail.rowData);
            });

            // 监听表格行添加
            document.addEventListener('rowAdded', function(e) {
                window.tableLogger.logRowAdd(e.detail.row, e.detail.rowData);
            });

            // 监听表格排序
            document.addEventListener('tableSorted', function(e) {
                window.tableLogger.logSort(e.detail.column, e.detail.direction);
            });

            // 监听表格过滤
            document.addEventListener('tableFiltered', function(e) {
                window.tableLogger.logFilter(e.detail.column, e.detail.filter);
            });

            // 修改生成配音表格的函数
            async function generateSpeechTable() {
                try {
                    const htmlText = document.getElementById('story-content').innerHTML;
                    const response = await fetch('/generate_speech_table', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ html_text: htmlText })
                    });

                    const data = await response.json();
                    if (data.status === 'success') {
                        window.tableLogger.setLogId(data.log_id);
                        window.tableLogger.logOperation('speech_table_generated', -1, 'all', '配音表格生成成功');
                        // ... 更新表格显示 ...
                    } else {
                        window.tableLogger.logOperation('speech_table_error', -1, 'all', data.message);
                        // ... 错误处理 ...
                    }
                } catch (error) {
                    window.tableLogger.logOperation('speech_table_exception', -1, 'all', error.message);
                    // ... 错误处理 ...
                }
            }

            // 修改生成SRT文件的函数
            async function generateSrtFiles() {
                try {
                    const htmlText = document.getElementById('speech-table').innerHTML;
                    const operations = window.tableLogger.getOperations();

                    const response = await fetch('/generate_srt_files', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            html_text: htmlText,
                            table_operations: operations
                        })
                    });

                    const data = await response.json();
                    if (data.status === 'success') {
                        window.tableLogger.setLogId(data.log_id);
                        window.tableLogger.logOperation('srt_files_generated', -1, 'all', 'SRT文件生成成功');
                        // ... 更新表格显示 ...
                    } else {
                        window.tableLogger.logOperation('srt_files_error', -1, 'all', data.message);
                        // ... 错误处理 ...
                    }
                } catch (error) {
                    window.tableLogger.logOperation('srt_files_exception', -1, 'all', error.message);
                    // ... 错误处理 ...
                }
            }

            // 修改音频预览函数
            async function previewAudio(filename) {
                try {
                    window.tableLogger.logOperation('audio_preview_start', -1, 'file', filename);
                    const audio = new Audio(`/preview_voice/${filename}`);

                    audio.onplay = () => {
                        window.tableLogger.logAudioPreview(filename, true);
                    };

                    audio.onerror = (e) => {
                        window.tableLogger.logAudioPreview(filename, false, e.message);
                    };

                    await audio.play();
                } catch (error) {
                    window.tableLogger.logAudioPreview(filename, false, error.message);
                    // ... 错误处理 ...
                }
            }

            // 修改SRT文件下载函数
            async function downloadSrtFile(filename) {
                try {
                    window.tableLogger.logOperation('srt_download_start', -1, 'file', filename);
                    const response = await fetch(`/srt_temp/${filename}`);

                    if (response.ok) {
                        window.tableLogger.logOperation('srt_download_success', -1, 'file', filename);
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    } else {
                        window.tableLogger.logOperation('srt_download_error', -1, 'file', filename);
                        // ... 错误处理 ...
                    }
                } catch (error) {
                    window.tableLogger.logOperation('srt_download_exception', -1, 'file', filename);
                    // ... 错误处理 ...
                }
            }

            // 显示提示词编辑弹窗
            function showPromptModal(prompts, index, tableDiv) {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'prompt-modal';
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                `;

                // 创建模态框内容
                const modalContent = document.createElement('div');
                modalContent.style.cssText = `
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    width: 80%;
                    max-width: 600px;
                    max-height: 80%;
                    overflow-y: auto;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                `;

                // 创建标题
                const title = document.createElement('h3');
                title.textContent = `编辑生图提示词 - ${prompts[index].page}`;
                title.style.cssText = `
                    margin: 0 0 15px 0;
                    color: #333;
                    font-size: 18px;
                `;

                // 创建文本框
                const textarea = document.createElement('textarea');
                textarea.value = prompts[index].prompt;
                textarea.style.cssText = `
                    width: 100%;
                    height: 200px;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 14px;
                    font-family: Arial, sans-serif;
                    resize: vertical;
                    box-sizing: border-box;
                `;

                // 创建按钮容器
                const buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = `
                    margin-top: 15px;
                    text-align: right;
                `;

                // 创建取消按钮
                const cancelBtn = document.createElement('button');
                cancelBtn.textContent = '取消';
                cancelBtn.style.cssText = `
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 10px;
                    font-size: 14px;
                `;

                // 创建确定按钮
                const confirmBtn = document.createElement('button');
                confirmBtn.textContent = '确定返回';
                confirmBtn.style.cssText = `
                    background: #2563eb;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                `;

                // 按钮事件
                cancelBtn.onclick = function() {
                    document.body.removeChild(modal);
                };

                confirmBtn.onclick = function() {
                    const newPrompt = textarea.value.trim();
                    if (newPrompt) {
                        // 更新prompts数组
                        prompts[index].prompt = newPrompt;

                        // 更新表格中对应行的内容
                        const table = tableDiv.querySelector('.prompt-table');
                        const rows = table.querySelectorAll('tbody tr');
                        if (rows[index]) {
                            const promptCell = rows[index].querySelector('td:nth-child(3)');
                            if (promptCell) {
                                promptCell.textContent = newPrompt;
                            }
                        }

                        console.log(`✅ 提示词已更新 - 序号${index + 1}: ${newPrompt.substring(0, 50)}...`);
                    }
                    document.body.removeChild(modal);
                };

                // 点击模态框外部关闭
                modal.onclick = function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                };

                // ESC键关闭
                document.addEventListener('keydown', function escHandler(e) {
                    if (e.key === 'Escape') {
                        document.body.removeChild(modal);
                        document.removeEventListener('keydown', escHandler);
                    }
                });

                // 组装模态框
                buttonContainer.appendChild(cancelBtn);
                buttonContainer.appendChild(confirmBtn);
                modalContent.appendChild(title);
                modalContent.appendChild(textarea);
                modalContent.appendChild(buttonContainer);
                modal.appendChild(modalContent);

                // 显示模态框
                document.body.appendChild(modal);

                // 聚焦到文本框
                setTimeout(() => {
                    textarea.focus();
                    textarea.select();
                }, 100);
            }
        });
    </script>
</body>
</html>