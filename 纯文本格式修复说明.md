# 纯文本格式修复说明

## 问题描述
"生成图片提示词表格"按钮点击后，无法正常获取上面LLM返回HTML消息表格内的内容，无法添加到生成图片提示词表格内。经过HTML格式修复后仍然无法获取到内容。

## 根本解决方案
修改Google LLM返回消息格式，从HTML格式改为纯文本格式，只使用换行符分段，避免HTML解析的复杂性。

## 修复内容

### 1. 修改LLM提示词（start.py 第313行）

**修改前：**
```python
prefix = "请根据中文排版规范，合理美化分段，并用基本HTML格式输出以下完整内容。只使用基本的HTML标签如<h1>、<h2>、<p>、<ul>、<li>、<table>、<tr>、<td>、<th>等，不要添加任何CSS类或样式属性："
```

**修改后：**
```python
prefix = "请根据中文排版规范，合理美化分段，并用纯文本格式输出以下完整内容。不要使用任何HTML标签，只使用换行符来分段。每个不同的内容段落之间用一个空行分隔："
```

### 2. 添加纯文本转HTML函数（start.py 第178-219行）

```python
def convert_text_to_html(raw_text: str) -> str:
    """
    将LLM返回的纯文本转换为简单的HTML格式用于显示。
    保持原始文本结构，只添加基本的HTML标签。
    """
    if not raw_text:
        return ""

    # 去除代码块包裹标记（如果有的话）
    cleaned = raw_text.strip()
    if cleaned.startswith('```'):
        lines = cleaned.split('\n')
        if lines[0].startswith('```'):
            lines = lines[1:]
        if lines and lines[-1].strip() == '```':
            lines = lines[:-1]
        cleaned = '\n'.join(lines)

    # 按行分割文本
    lines = cleaned.split('\n')
    html_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            # 空行保持为空行
            html_lines.append('')
        elif line.startswith('标题：'):
            # 标题处理
            title = line.replace('标题：', '').strip()
            html_lines.append(f'<h1>{title}</h1>')
        elif line.startswith('第') and ('页' in line):
            # 页数标题
            html_lines.append(f'<h2>{line}</h2>')
        elif ':' in line or '：' in line:
            # 包含冒号的行，通常是角色对白或描述
            html_lines.append(f'<p><strong>{line}</strong></p>')
        else:
            # 普通段落
            html_lines.append(f'<p>{line}</p>')
    
    return '\n'.join(html_lines)
```

### 3. 修改内容处理逻辑（start.py 第337-349行）

**修改前：**
```python
if full_text:
    formatted_html = clean_llm_html(full_text)
    results.append({
        "type": "text",
        "content": formatted_html,
        "display_content_once": True
    })
```

**修改后：**
```python
if full_text:
    # 将纯文本转换为简单的HTML格式用于显示
    formatted_html = convert_text_to_html(full_text)
    results.append({
        "type": "text",
        "content": formatted_html,  # 转换后的HTML用于显示
        "raw_content": full_text,  # 保存原始纯文本内容用于后续处理
        "display_content_once": True
    })
```

### 4. 修改前端存储逻辑（templates/index.html 第249-251行）

```javascript
// 存储原始纯文本内容用于解析
richEditorDiv.setAttribute('data-raw-content', item.raw_content || '');
richEditorDiv.innerHTML = item.content;
```

### 5. 修改按钮解析逻辑（templates/index.html 第278-339行）

**关键修改：**
```javascript
// 优先使用原始纯文本内容
const rawContent = latestRichEditor ? latestRichEditor.getAttribute('data-raw-content') : '';
const html = latestRichEditor ? latestRichEditor.innerHTML : '';

const contentToProcess = rawContent || html;

// 按行分割内容
const lines = contentToProcess.split('\n');

lines.forEach(line => {
    const txt = line.trim();
    // ... 解析逻辑
});
```

## 修复效果

### 格式对比

**修复前（HTML格式）：**
```html
<h1>《小兔子贝贝的彩虹冒险》</h1>
<h2>第1页：</h2>
<p><strong>主持人角色对白：</strong>在一个阳光明媚的早晨...</p>
<p><strong>生图Prompt详细描述：</strong>【图片风格为...】</p>
```

**修复后（纯文本格式）：**
```
标题：《小兔子贝贝的彩虹冒险》

第1页：

主持人角色对白：在一个阳光明媚的早晨，小兔子贝贝在花园里发现了一道神奇的彩虹门。

主持人角色配音要求：全篇统一为某个音色，其特色为：成熟稳重、有说服力。

生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳，小兔子贝贝站在花园中，面前有一道七彩彩虹门，阳光透过云层洒下，花朵盛开，中景，特写，超精细，大师构图，背景虚幻，比例 "16:9"】
```

### 优势对比

| 方面 | HTML格式 | 纯文本格式 |
|------|----------|------------|
| 解析复杂度 | 高（需要DOM解析） | 低（按行处理） |
| 错误率 | 高（HTML标签干扰） | 低（直接文本匹配） |
| 维护性 | 差（依赖HTML结构） | 好（简单文本处理） |
| 兼容性 | 差（HTML变化影响解析） | 好（文本格式稳定） |
| 调试难度 | 高（HTML结构复杂） | 低（文本内容直观） |

## 支持的提示词格式

1. ✅ **带括号格式**：`生图Prompt详细描述：【内容】`
2. ✅ **无括号格式**：`生图Prompt详细描述：内容`
3. ✅ **场景配图格式**：`场景配图，生图Prompt详细描述：【内容】`

## 测试验证

### 测试文件：`test_plain_text_fix.html`
包含完整的测试场景，验证：
- 纯文本内容解析
- 多种格式提示词匹配
- 表格生成功能
- 调试信息输出

### 验证步骤
1. 打开测试文件：`test_plain_text_fix.html`
2. 点击"生成图片提示词表格"按钮
3. 查看解析结果和生成的表格
4. 检查浏览器控制台的调试信息

### 预期结果
- ✅ 解析到3个提示词（带括号、无括号、场景配图各1个）
- ✅ 生成完整的提示词表格
- ✅ 控制台输出详细的调试信息
- ✅ 支持所有格式的提示词

## 技术要点

1. **纯文本优先**：优先使用`data-raw-content`属性中的原始纯文本
2. **按行处理**：避免复杂的HTML DOM解析
3. **多格式支持**：正则表达式匹配多种提示词格式
4. **向后兼容**：如果没有纯文本，仍然支持HTML解析
5. **调试友好**：详细的控制台输出便于问题排查

## 相关文件

- `start.py` - 后端修复（LLM提示词、内容处理）
- `templates/index.html` - 前端修复（内容存储、解析逻辑）
- `test_plain_text_fix.html` - 测试验证文件
- `纯文本格式修复说明.md` - 本说明文档

## 总结

通过将LLM返回格式从HTML改为纯文本，彻底解决了"生成图片提示词表格"按钮无法正确获取内容的问题。这个修复：

1. **简化了解析逻辑** - 从复杂的HTML DOM解析改为简单的文本行处理
2. **提高了可靠性** - 避免了HTML标签和格式变化带来的解析错误
3. **增强了兼容性** - 支持多种格式的生图提示词
4. **改善了调试体验** - 纯文本内容更直观，便于问题排查

现在"生成图片提示词表格"功能应该能够稳定可靠地工作了！
