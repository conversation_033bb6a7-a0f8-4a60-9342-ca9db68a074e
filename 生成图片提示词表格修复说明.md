# 生成图片提示词表格修复说明

## 问题描述
"生成图片提示词表格"按钮点击后，无法正常获取上面LLM返回HTML消息表格内的内容，无法添加到生成图片提示词表格内。

## 问题分析

### 根本原因
1. **正则表达式匹配问题**：原有的正则表达式 `/生图Prompt详细描述[：:][^【]*([【][^】]+[】])/` 只能匹配带【】括号的格式
2. **基本HTML修复的影响**：由于之前修复了LLM返回基本HTML（移除CSS类和样式），LLM现在可能返回不带括号的格式
3. **格式多样性**：LLM可能返回多种不同格式的生图提示词，原有匹配逻辑无法覆盖所有情况

### 具体表现
- 按钮点击后提示"未找到可解析的内容"
- 即使有生图提示词内容，也无法正确解析
- 表格无法生成或生成空表格

## 修复方案

### 1. 改进正则表达式匹配逻辑
支持多种格式的生图提示词：

**格式1：带【】括号的格式**
```
生图Prompt详细描述：【图片风格为「3D渲染」，色彩鲜艳...】
```

**格式2：无括号的格式**
```
生图Prompt详细描述：图片风格为3D渲染，色彩鲜艳...
```

**格式3：场景配图格式**
```
场景配图，生图Prompt详细描述：【内容】
```

### 2. 添加调试功能
- 输出获取到的HTML内容
- 显示处理的每个段落
- 记录找到的提示词
- 统计解析结果

### 3. 增强错误处理
- 详细的错误提示信息
- 指导用户如何排查问题
- 提示查看浏览器控制台

## 具体修改

### 修改文件：`templates/index.html`

#### 1. 添加调试输出（第277-279行）
```javascript
// 添加调试输出
console.log('获取到的HTML内容:', html);
console.log('富文本编辑区数量:', allRichEditors.length);
```

#### 2. 改进匹配逻辑（第301-320行）
```javascript
// 改进的生图提示词匹配逻辑，支持多种格式
let promptText = '';

// 格式1：生图Prompt详细描述：【内容】
let promptMatch1 = txt.match(/生图Prompt详细描述[：:]\s*[【]([^】]+)[】]/);
if (promptMatch1) {
    promptText = promptMatch1[1].trim();
} else {
    // 格式2：生图Prompt详细描述：内容（无括号）
    let promptMatch2 = txt.match(/生图Prompt详细描述[：:]\s*(.+?)(?:\s*$|\s*。|\s*，)/);
    if (promptMatch2) {
        promptText = promptMatch2[1].trim();
    } else {
        // 格式3：场景配图，生图Prompt详细描述：内容
        let promptMatch3 = txt.match(/场景配图[，,]\s*生图Prompt详细描述[：:]\s*[【]?([^】\n]+)[】]?/);
        if (promptMatch3) {
            promptText = promptMatch3[1].trim();
        }
    }
}
```

#### 3. 增强错误处理（第335-339行）
```javascript
// 检查是否找到提示词
if (prompts.length === 0) {
    alert('未找到生图提示词！\n\n可能的原因：\n1. 内容中没有包含"生图Prompt详细描述"字样\n2. 格式不正确\n\n请检查生成的内容是否包含生图提示词。\n\n调试信息已输出到浏览器控制台，请按F12查看。');
    return;
}
```

#### 4. 添加成功提示（第369行）
```javascript
// 显示成功消息
console.log('✅ 图片提示词表格生成成功！解析到', prompts.length, '个提示词');
```

## 修复效果

### 支持的格式
1. ✅ **带括号格式**：`生图Prompt详细描述：【内容】`
2. ✅ **无括号格式**：`生图Prompt详细描述：内容`
3. ✅ **场景配图格式**：`场景配图，生图Prompt详细描述：【内容】`
4. ✅ **混合格式**：同一文档中包含多种格式

### 调试功能
1. ✅ **HTML内容输出**：显示实际获取的HTML内容
2. ✅ **段落处理日志**：记录每个段落的处理过程
3. ✅ **提示词发现日志**：显示找到的每个提示词
4. ✅ **统计信息**：显示解析到的提示词数量

### 错误处理
1. ✅ **详细错误信息**：说明可能的原因和解决方法
2. ✅ **调试指导**：提示用户查看浏览器控制台
3. ✅ **成功反馈**：显示解析成功的信息

## 测试验证

### 测试文件：`test_prompt_table_fix.html`
包含4个测试场景：
1. **场景1**：带【】括号的格式
2. **场景2**：无括号的格式
3. **场景3**：场景配图格式
4. **场景4**：多页混合格式

### 验证步骤
1. 打开测试文件：`test_prompt_table_fix.html`
2. 点击各个场景的"生成图片提示词表格"按钮
3. 查看解析结果和生成的表格
4. 检查浏览器控制台的调试信息

### 预期结果
- ✅ 所有格式都能正确解析
- ✅ 生成完整的提示词表格
- ✅ 控制台输出详细的调试信息
- ✅ 错误情况有明确的提示

## 兼容性说明

### 向前兼容
- ✅ 支持原有的【】括号格式
- ✅ 兼容之前的LLM返回内容
- ✅ 保持原有的表格生成逻辑

### 向后兼容
- ✅ 支持基本HTML格式的内容
- ✅ 适应LLM输出格式的变化
- ✅ 灵活的匹配策略

## 技术要点

1. **正则表达式优化**：使用多个正则表达式覆盖不同格式
2. **容错处理**：即使部分格式不匹配，也能处理其他格式
3. **调试友好**：详细的日志输出便于问题排查
4. **用户体验**：清晰的错误提示和成功反馈

## 相关文件

- `templates/index.html` - 主要修复文件
- `test_prompt_table_fix.html` - 测试验证文件
- `生成图片提示词表格修复说明.md` - 本说明文档

这个修复确保了"生成图片提示词表格"功能能够正确工作，无论LLM返回什么格式的内容，都能准确解析并生成提示词表格。
