# 修复说明：生成图片提示词表格按钮问题

## 问题描述
"生成图片提示词表格"按钮点击后，无法正确获取上面LLM返回的HTML表格内容，无法正确添加到生成图片提示词表格中。

## 问题根源
通过代码分析发现，问题出现在按钮的作用域和内容获取逻辑上：

1. **按钮只创建一次**：在 `templates/index.html` 第198行，使用了条件判断 `if (!document.querySelector('.gen-prompt-table-btn'))`，导致按钮只在第一次生成内容时创建。

2. **闭包引用固定**：按钮创建时绑定了第一次生成内容的 `richEditorDiv` 引用，后续生成新内容时，按钮仍然引用第一次的HTML内容。

3. **内容获取错误**：当用户多次生成内容时，按钮获取的始终是第一次的HTML内容，而不是最新的LLM返回内容。

## 修复方案
采用动态内容获取策略，修改按钮的点击逻辑：

### 1. 修改"生成图片提示词表格"按钮
**文件位置**：`templates/index.html` 第211-215行

**修改前**：
```javascript
genPromptTableBtn.onclick = function() {
    // 解析HTML，生成表格
    const html = richEditorDiv.innerHTML;
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
```

**修改后**：
```javascript
genPromptTableBtn.onclick = function() {
    // 动态获取最新的富文本编辑区内容
    const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
    const latestRichEditor = allRichEditors[allRichEditors.length - 1];
    const html = latestRichEditor ? latestRichEditor.innerHTML : '';
    
    if (!html) {
        alert('未找到可解析的内容，请先生成故事内容！');
        return;
    }
    
    // 解析HTML，生成表格
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
```

### 2. 修改"配音表生成"按钮
**文件位置**：`templates/index.html` 第295-299行

**修改前**：
```javascript
nextStepBtn.onclick = async function() {
    nextStepBtn.disabled = true;
    nextStepBtn.textContent = '正在生成配音表...';
    // 取原始HTML内容
    const htmlContent = richEditorDiv.innerHTML;
```

**修改后**：
```javascript
nextStepBtn.onclick = async function() {
    nextStepBtn.disabled = true;
    nextStepBtn.textContent = '正在生成配音表...';
    // 动态获取最新的富文本编辑区内容
    const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
    const latestRichEditor = allRichEditors[allRichEditors.length - 1];
    const htmlContent = latestRichEditor ? latestRichEditor.innerHTML : '';
    
    if (!htmlContent) {
        alert('未找到可解析的内容，请先生成故事内容！');
        nextStepBtn.disabled = false;
        nextStepBtn.textContent = '配音表生成';
        return;
    }
```

### 3. 修改"SRT配音表生成"按钮
**文件位置**：`templates/index.html` 第334-341行

**修改前**：
```javascript
nextSrtBtn.onclick = async function() {
    nextSrtBtn.disabled = true;
    nextSrtBtn.textContent = '正在生成SRT及配音表...';
    try {
        const srtResp = await fetch('/generate_srt_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ html_text: htmlContent })
        });
```

**修改后**：
```javascript
nextSrtBtn.onclick = async function() {
    nextSrtBtn.disabled = true;
    nextSrtBtn.textContent = '正在生成SRT及配音表...';
    
    // 动态获取最新的富文本编辑区内容
    const allRichEditors = document.querySelectorAll('.rich-editor-hidden');
    const latestRichEditor = allRichEditors[allRichEditors.length - 1];
    const currentHtmlContent = latestRichEditor ? latestRichEditor.innerHTML : '';
    
    if (!currentHtmlContent) {
        alert('未找到可解析的内容，请先生成故事内容！');
        nextSrtBtn.disabled = false;
        nextSrtBtn.textContent = 'SRT配音表生成';
        return;
    }
    
    try {
        const srtResp = await fetch('/generate_srt_files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ html_text: currentHtmlContent })
        });
```

### 4. 改进错误处理
为所有按钮添加了更完善的错误处理，确保在出错时能正确重置按钮状态：

- 添加了 `nextStepBtn.disabled = false` 和 `nextSrtBtn.disabled = false`
- 确保按钮在出错后能重新点击

## 修复效果
1. **动态内容获取**：按钮现在能动态获取最新生成的LLM内容，而不是固定引用第一次的内容
2. **多次生成支持**：用户可以多次生成故事内容，每次点击按钮都能正确处理最新的内容
3. **错误提示优化**：当没有可解析内容时，会显示友好的错误提示
4. **按钮状态管理**：改进了按钮的禁用/启用状态管理，避免按钮卡死

## 测试验证
创建了 `test_fix.html` 测试文件，可以验证修复效果：

1. 模拟多次生成内容的场景
2. 测试按钮是否能获取最新内容
3. 验证提示词解析是否正确

## 技术要点
- 使用 `document.querySelectorAll('.rich-editor-hidden')` 获取所有富文本编辑区
- 通过 `allRichEditors[allRichEditors.length - 1]` 获取最新的编辑区
- 添加内容验证，确保有可解析的内容
- 改进错误处理和用户体验

这个修复确保了"生成图片提示词表格"功能能够正确工作，无论用户生成多少次内容，按钮都能获取到最新的LLM返回内容。
