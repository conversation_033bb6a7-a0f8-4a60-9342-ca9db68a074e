import logging
import os
import time
from typing import Dict, List
from pypinyin import lazy_pinyin
import re
from bs4 import BeautifulSoup

def normalize_character_name(name: str) -> str:
    """
    统一处理角色名称，去除后缀和修饰词
    
    参数:
        name (str): 原始角色名称
        
    返回:
        str: 标准化后的角色名称
    """
    # 移除常见的后缀和修饰词
    name = re.sub(r'(?:角色)?(?:详细对白|对白|配音要求|配音)$', '', name)
    # 移除括号内容
    name = re.sub(r'[（(].*?[)）]', '', name)
    # 处理主持人角色的特殊情况
    if '主持人' in name:
        return '主持人'
    # 移除多余空格
    name = name.strip()
    return name

def extract_voice_requirement(text: str) -> str:
    """
    从文本中提取配音要求
    
    参数:
        text (str): 包含配音要求的文本
        
    返回:
        str: 提取的配音要求
    """
    # 主持人配音要求模式
    host_voice_pattern = r'(?:主持人角色配音要求)[：:】]?\s*(.*?)(?=\s*(?:每页包含|$))'
    # 角色配音要求模式
    character_voice_pattern = r'(?:([^【】]+?)(?:角色)?配音要求)[：:】]?\s*(.*?)(?=\s*(?:每页包含|$))'
    
    # 尝试匹配主持人配音要求
    host_match = re.search(host_voice_pattern, text)
    if host_match:
        return host_match.group(1).strip()
    
    # 尝试匹配角色配音要求
    character_match = re.search(character_voice_pattern, text)
    if character_match:
        return character_match.group(2).strip()
    
    return ""

def extract_paired_content(text: str) -> tuple:
    """
    提取成对出现的内容，包括说话内容和配音要求
    
    参数:
        text (str): 包含成对内容的文本
        
    返回:
        tuple: (speaker, content, voice_req) 或 None
    """
    # 清理文本，移除多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 1. 优先匹配主持人角色和配音要求的配对
    host_pattern = r'(?:【)?主持人角色[：:】]?\s*(.*?)(?:\s*主持人角色配音要求[：:】]?\s*(.*?))?(?:【|$)'
    host_match = re.search(host_pattern, text, re.DOTALL)
    if host_match:
        content = host_match.group(1).strip()
        voice_req = host_match.group(2).strip() if host_match.group(2) else ""
        if content:  # 只处理有内容的情况
            return "主持人", content, voice_req
    
    # 2. 匹配角色对白和配音要求的配对
    char_pattern = r'(?:【)?([^【】]+?)(?:角色)?(?:对白|详细对白)[：:】]?\s*(.*?)(?:\s*\1(?:角色)?配音要求[：:】]?\s*(.*?))?(?:【|$)'
    char_match = re.search(char_pattern, text, re.DOTALL)
    if char_match:
        character = normalize_character_name(char_match.group(1))
        content = char_match.group(2).strip()
        voice_req = char_match.group(3).strip() if char_match.group(3) else ""
        if content:  # 只处理有内容的情况
            return character, content, voice_req
    
    return None

def clean_dialogue_content(content: str) -> str:
    """
    去除说话内容中的【对白：】、[对白:]、[对白：]、【对白:]等所有常见变体及其后内容，并且无论"对白："出现在哪里都要删除
    """
    import re
    # 先去除所有常见括号包裹的对白变体及其后内容
    pattern = r'[\[【{(（][ ]*对白[：:][^\]】})）]*[\]】})）]?'  # 匹配如【对白：、[对白:]等
    content = re.sub(pattern, '', content)
    # 再全局去除所有"对白："
    content = content.replace('对白：', '').replace('对白:', '')
    return content.strip()

def generate_speech_table(html_text: str) -> str:
    """
    解析LLM返回的HTML文本，生成带有SRT时间区间的说话表格

    规则：
    1. 内容配对规则：
       a. 主持人角色配对：
          主持人角色：页面内容概述
          主持人角色配音要求：配音要求
       
       b. 角色对白配对：
          XXX角色对白：内容
          XX角色配音要求：配音要求

    2. 角色名称处理：
       - 主持人角色 -> 主持人（说话人）
       - XXX角色对白 -> XXX（说话人）
       - 配音要求内容放入配音要求列

    参数:
        html_text (str): LLM返回的HTML文本

    返回:
        str: 包含表格的HTML字符串
    """
    soup = BeautifulSoup(html_text, "html.parser")

    # 提取所有段落文本，过滤掉场景配图相关内容
    paragraphs = []
    for p in soup.find_all("p"):
        text = p.get_text(strip=True)
        if text and not any(keyword in text for keyword in [
            "场景配图", "生图Prompt", "图片风格", "3D渲染", 
            "色彩鲜艳", "专业光影", "画面元素", "比例"
        ]):
            paragraphs.append(text)

    # 处理配音表格数据
    table_data = []
    voice_requirements = {}  # 用于存储所有配音要求

    # 第一遍扫描：收集所有配音要求
    for text in paragraphs:
        # 检查是否包含配音要求
        if "配音要求" in text:
            # 提取配音要求
            if "主持人角色配音要求" in text:
                voice_req = extract_voice_requirement(text)
                if voice_req:
                    voice_requirements["主持人"] = voice_req
            else:
                # 匹配角色配音要求
                character_match = re.search(r'(?:【)?([^【】]+?)(?:角色)?配音要求[：:】]?', text)
                if character_match:
                    character = normalize_character_name(character_match.group(1))
                    voice_req = extract_voice_requirement(text)
                    if voice_req:
                        voice_requirements[character] = voice_req

    # 第二遍扫描：处理对话内容
    i = 0
    while i < len(paragraphs):
        current_text = paragraphs[i]
        # 跳过只包含配音要求的段落（防止被当作说话内容）
        if re.match(r'^(主持人)?[^：:】]*配音要求[：:】]', current_text):
            i += 1
            continue
        # 尝试提取成对出现的内容
        paired_content = extract_paired_content(current_text)
        if paired_content:
            speaker, content, voice_req = paired_content
            cleaned_content = clean_dialogue_content(content)
            if not cleaned_content:
                i += 1
                continue
            if not voice_req:  # 如果成对内容中没有配音要求，尝试使用预收集的
                voice_req = voice_requirements.get(speaker, "")
            table_data.append((speaker, cleaned_content, voice_req))
            i += 1
            continue

        # 如果不是成对内容，尝试单独提取说话内容
        # 匹配主持人角色
        host_match = re.search(r'(?:【)?主持人角色[：:】]?\s*(.*?)(?=\s*(?:每页包含|$))', current_text)
        if host_match and "配音要求" not in current_text:  # 确保不是配音要求行
            content = host_match.group(1).strip()
            cleaned_content = clean_dialogue_content(content)
            if cleaned_content:
                voice_req = voice_requirements.get("主持人", "")
                table_data.append(("主持人", cleaned_content, voice_req))
            i += 1
            continue

        # 匹配角色对白
        dialogue_match = re.search(r'(?:【)?([^【】]+?)(?:角色)?(?:详细对白|对白)[：:】]?\s*(.*?)(?=\s*(?:每页包含|$))', current_text)
        if dialogue_match:
            character = normalize_character_name(dialogue_match.group(1))
            dialogue = dialogue_match.group(2).strip()
            cleaned_content = clean_dialogue_content(dialogue)
            if cleaned_content:
                voice_req = voice_requirements.get(character, "")
                table_data.append((character, cleaned_content, voice_req))
        i += 1

    # 计算每段的时间区间
    table_rows = []
    prev_end_time = 0.0  # 上一行的结束时间
    for idx, (speaker, content, voice_req) in enumerate(table_data, 1):
        # 统一调用calculate_time_intervals函数计算配音时长
        duration = calculate_time_intervals(content)
        start_time = prev_end_time + 1  # 本行开始时间=上一行结束时间+1秒
        end_time = start_time + duration
        def format_srt_time(t: float) -> str:
            h = int(t // 3600)
            m = int((t % 3600) // 60)
            s = int(t % 60)
            ms = int((t - int(t)) * 1000)
            return f"{h:02}:{m:02}:{s:02},{ms:03}"
        time_range = f"{format_srt_time(start_time)} --> {format_srt_time(end_time)}"
        table_rows.append(
            f"<tr><td>{idx}</td><td>{time_range}</td><td>{speaker}</td><td>{content}</td><td>{voice_req}</td></tr>"
        )
        prev_end_time = end_time  # 更新上一行的结束时间

    table_html = """
    <table class="table-auto w-full border border-gray-300 mt-4">
        <thead>
            <tr class="bg-gray-100">
                <th class="border px-2 py-1">序号</th>
                <th class="border px-2 py-1">时间</th>
                <th class="border px-2 py-1">角色说话人</th>
                <th class="border px-2 py-1">说话内容</th>
                <th class="border px-2 py-1">配音要求</th>
            </tr>
        </thead>
        <tbody>
            %s
        </tbody>
    </table>
    """ % "\n".join(table_rows)
    return table_html

VOICE_OPTIONS = [
    # --- 男童声 ---
    ("601015", "爱小童 男童声"),
    ("101015", "智萌 男童声"),

    # --- 女童声 ---
    ("101016", "智甜 女童声"),

    # --- 情感男声 ---
    ("101018", "智靖 情感男声"),

    # --- 情感女声 ---
    ("101001", "智瑜 情感女声"),
    ("101017", "智蓉 情感女声"),

    # --- 聊天男声 ---
    ("301034", "爱小杭 聊天男声"),
    ("301035", "爱小杭 聊天女声"),
    ("301036", "爱小杭 旁白男声"),
    ("301037", "爱小杭 旁白女声"),
    ("301038", "爱小杭 童声男"),
    ("301039", "爱小杭 童声女"),
    ("301040", "爱小杭 老年男声"),
    ("301041", "爱小杭 老年女声"),
    ("301042", "爱小杭 青年男声"),
    ("301043", "爱小杭 青年女声"),
]

def chinese_to_pinyin(text: str) -> str:
    """将中文转为拼音全拼，全部小写，无空格"""
    return ''.join(lazy_pinyin(text))

def calculate_time_intervals(text: str) -> float:
    """
    根据文本内容计算配音所需时长
    
    采用更贴近实际配音的时长估算：
    - 每个汉字约0.18秒
    - 标点符号约0.12秒
    - 最短2秒
    - 长句适当加快朗读速度
    
    参数:
        text (str): 需要计算时长的文本内容
    
    返回:
        float: 估算的配音时长（秒）
    """
    import re
    # 统计汉字和标点数量
    hanzi_count = len(re.findall(r'[\u4e00-\u9fff]', text))
    punc_count = len(re.findall(r'[，。！？、；："”''（）—…,.!?;:()\-]', text))
    other_count = max(0, len(text) - hanzi_count - punc_count)
    # 汉字0.18s，标点0.12s，其他字符0.15s
    duration = hanzi_count * 0.18 + punc_count * 0.12 + other_count * 0.15
    # 长句加速：超20字部分每字0.13s
    total_chars = hanzi_count + other_count
    if total_chars > 20:
        duration = hanzi_count * 0.15 + punc_count * 0.12 + other_count * 0.13
    duration = max(2.0, duration)
    return duration

def generate_srt_files_and_table(html_text: str, srt_dir: str = "srt_temp", story_title: str = "all") -> str:
    """
    解析配音表，按角色分组生成SRT文件，返回新表格HTML
    新增：生成一个所有角色对白合并的SRT文件，文件名为故事名拼音+时间戳.srt，表格第一行展示。
    """
    if not os.path.exists(srt_dir):
        os.makedirs(srt_dir)
    soup = BeautifulSoup(html_text, "html.parser")
    paragraphs = []
    for p in soup.find_all("p"):
        text = p.get_text(strip=True)
        if text:
            paragraphs.append(text)
    # 解析角色对白及时间（修正：支持页面概述）
    table_data = []
    prev_end_time = 0.0
    i = 0
    while i < len(paragraphs):
        current_text = paragraphs[i]
        # 跳过配音要求相关的内容
        if "配音要求" in current_text:
            i += 1
            continue
        # 页面详细概述
        page_overview_match = re.search(r'(?:【)?(页面详细概述)[：:】]?\s*(.*)', current_text)
        if page_overview_match:
            overview_content = page_overview_match.group(2).strip()
            # 移除括号内的内容（包括中英文括号）
            overview_content = re.sub(r'[（(].*?[)）]', '', overview_content)
            # 移除可能的配音要求标记
            overview_content = re.sub(r'配音要求.*$', '', overview_content)
            overview_content = overview_content.strip()
            if overview_content:  # 确保内容不为空
                char_count = len(overview_content)
                duration = max(2.0, char_count * 0.3)
                if char_count > 20:
                    duration = 6.0 + (char_count - 20) * 0.25
                start_time = prev_end_time + 1
                end_time = start_time + duration
                prev_end_time = end_time
                table_data.append({
                    "character": "页面概述",
                    "dialogue": overview_content,
                    "start_time": start_time,
                    "end_time": end_time
                })
            i += 1
            continue
        # 角色详细对白
        character_dialogue_match = re.search(r'(?:【)?([^【】]+?)(?:角色详细对白|角色对白)[：:】]?\s*(.*)', current_text)
        if character_dialogue_match:
            character = character_dialogue_match.group(1).strip()
            dialogue = character_dialogue_match.group(2).strip()
            dialogue = re.sub(r'[（(].*?[)）]', '', dialogue)
            dialogue = re.sub(r'配音要求.*$', '', dialogue)
            dialogue = dialogue.strip()
            if dialogue:
                char_count = len(dialogue)
                duration = max(2.0, char_count * 0.3)
                if char_count > 20:
                    duration = 6.0 + (char_count - 20) * 0.25
                start_time = prev_end_time + 1
                end_time = start_time + duration
                prev_end_time = end_time
                table_data.append({
                    "character": character,
                    "dialogue": dialogue,
                    "start_time": start_time,
                    "end_time": end_time
                })
            i += 1
            continue
        # 其它角色
        character_name_match = re.search(r'^([^【】]+?)(?:角色|说话人)[：:】]?\s*(.*)', current_text)
        if character_name_match:
            character = character_name_match.group(1).strip()
            dialogue = character_name_match.group(2).strip()
            dialogue = re.sub(r'[（(].*?[)）]', '', dialogue)
            dialogue = re.sub(r'配音要求.*$', '', dialogue)
            dialogue = dialogue.strip()
            if dialogue:
                char_count = len(dialogue)
                duration = max(2.0, char_count * 0.3)
                if char_count > 20:
                    duration = 6.0 + (char_count - 20) * 0.25
                start_time = prev_end_time + 1
                end_time = start_time + duration
                prev_end_time = end_time
                table_data.append({
                    "character": character,
                    "dialogue": dialogue,
                    "start_time": start_time,
                    "end_time": end_time
                })
        i += 1

    # 按角色分组
    role_map: Dict[str, List[dict]] = {}
    for row in table_data:
        if row["dialogue"].strip():  # 只处理非空内容
            role_map.setdefault(row["character"], []).append(row)

    # 生成所有角色合并SRT文件
    import time as _time
    story_pinyin = ''.join(lazy_pinyin(story_title)) if story_title else 'all'
    timestamp = int(_time.time())
    all_srt_filename = f"{story_pinyin}_{timestamp}.srt"
    all_srt_path = os.path.join(srt_dir, all_srt_filename)
    with open(all_srt_path, "w", encoding="utf-8") as f:
        for idx, row in enumerate(table_data, 1):
            def format_srt_time(t: float) -> str:
                h = int(t // 3600)
                m = int((t % 3600) // 60)
                s = int(t % 60)
                ms = int((t - int(t)) * 1000)
                return f"{h:02}:{m:02}:{s:02},{ms:03}"
            f.write(f"{idx}\n{format_srt_time(row['start_time'])} --> {format_srt_time(row['end_time'])}\n[{row['character']}] {row['dialogue']}\n\n")

    # 生成SRT文件（分角色）
    srt_info = []
    for role, items in role_map.items():
        if not items:  # 跳过空内容
            continue
        pinyin_name = chinese_to_pinyin(role)
        srt_filename = f"{pinyin_name}_{timestamp}.srt"
        srt_path = os.path.join(srt_dir, srt_filename)
        with open(srt_path, "w", encoding="utf-8") as f:
            for idx, item in enumerate(items, 1):
                def format_srt_time(t: float) -> str:
                    h = int(t // 3600)
                    m = int((t % 3600) // 60)
                    s = int(t % 60)
                    ms = int((t - int(t)) * 1000)
                    return f"{h:02}:{m:02}:{s:02},{ms:03}"
                f.write(f"{idx}\n{format_srt_time(item['start_time'])} --> {format_srt_time(item['end_time'])}\n{item['dialogue']}\n\n")
        srt_info.append({
            "role": role,
            "srt_filename": srt_filename
        })

    # 生成SRT文件表格，第一行为所有角色合并SRT
    table_rows = []
    all_srt_url = f"/srt_temp/{all_srt_filename}"
    table_rows.append(f"<tr><td>0</td><td><a href='{all_srt_url}' target='_blank'>{all_srt_filename}</a><span style='color:#f59e42'>&nbsp;[全角色汇总]</span></td></tr>")
    for idx, info in enumerate(srt_info, 1):
        srt_url = f"/srt_temp/{info['srt_filename']}"
        table_rows.append(f"<tr><td>{idx}</td><td><a href='{srt_url}' target='_blank'>{info['srt_filename']}</a></td></tr>")
    table_html = """
    <table class="table-auto w-full border border-gray-300 mt-4">
        <thead>
            <tr class="bg-gray-100">
                <th class="border px-2 py-1">序号</th>
                <th class="border px-2 py-1">SRT文件</th>
            </tr>
        </thead>
        <tbody>
            %s
        </tbody>
    </table>
    """ % "\n".join(table_rows)
    return table_html
